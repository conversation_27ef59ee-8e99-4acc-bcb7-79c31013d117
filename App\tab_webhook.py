
import MetaTrader5 as mt5
import customtkinter as ctk
import pandas as pd
import numpy as np
import talib as ta
import threading
import time
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import gc
from flask import Flask, request, jsonify
from waitress import serve
import os
import random
import string

# ===========================
# Class: TabWebhook
# ===========================
class TabWebhook:
    def __init__(self, master, config, util):
        self.frame = master.add("Webhook")
        self.config = config
        self.util = util
        self.webhook_enabled = True
        self.app = Flask(__name__)
        self.name = "WH"
        self.side_var = ctk.StringVar()
        self.symbol_var = ctk.StringVar()
        self.lot_var = ctk.DoubleVar()
        # self.point_bsl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_btp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        # self.point_ssl_var = ctk.IntVar(value=self.config.SL_POINTS)
        # self.point_stp_var = ctk.IntVar(value=self.config.TP1_POINTS)
        self.time_var = ctk.IntVar()
        self.loop_running = False
        self.auto_be_enabled = False

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=10)  

        self.build_ui()
        self.build_router()
        
        # --- รัน Flask ใน background thread ---
        # flask_thread = threading.Thread(target=self.run_webhook, daemon=True)
        # flask_thread.start()
        

    def build_ui(self):
        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=0, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=1, padx=10, pady=5)

        # self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        # self.symbol_label.grid(row=1, column=2, padx=10, pady=5)
        # self.symbol_var.set("XU")  # Default symbol
        # self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        # self.symbol_dropdown.grid(row=1, column=3, padx=10, pady=5)
        
        # self.time_var.set(1)  # Default 5 min
        # self.time_label = ctk.CTkLabel(self.form1, text="Loop BE (min):")
        # self.time_label.grid(row=2, column=0, padx=10, pady=5)
        # self.time_val = ctk.CTkEntry(self.form1, textvariable=self.time_var)
        # self.time_val.grid(row=2, column=1, padx=10, pady=5)

        self.side_label = ctk.CTkLabel(self.form1, text="Side:")
        self.side_label.grid(row=1, column=2, padx=10, pady=5)
        self.side_var.set("BUY")  # Default symbol
        self.side_dropdown = ctk.CTkOptionMenu(self.form1, values=["BUY","SELL","BOTH"], variable=self.side_var)
        self.side_dropdown.grid(row=1, column=3, padx=10, pady=5)
  
        # self.status_label = ctk.CTkLabel(self.form3, text="🔘 Not Monitoring")
        # self.status_label.pack(pady=1) 

        self.switch_webhook = ctk.CTkSwitch(self.frame, text="Enable Webhook", command=self.toggle_webhook)
        self.switch_webhook.pack(padx=10, pady=10)
        self.switch_webhook.select()

    # Webhook Callback function
    def toggle_webhook(self):
        if self.switch_webhook.get():
            self.enable_webhook()
        else:
            self.disable_webhook()

    def run_webhook(self):
        serve(self.app, host="0.0.0.0", port=5000)
        # self.app.run(host="0.0.0.0", port=5000, debug=False, use_reloader=False)
        self.toggle_webhook()

    def enable_webhook(self):
        self.webhook_enabled = True
        self.util.add_status_frame(f"🟢 Webhook ENABLED")

    def disable_webhook(self):
        self.webhook_enabled = False
        self.util.add_status_frame(f"🔴 Webhook DISABLED")

    def verify_access_token(self):
        """
        Verify access token from both Authorization header (Bearer token) and X-Access-Token header
        Returns tuple: (is_valid, error_message)
        """
        # Get the expected access token from environment
        expected_token = os.getenv('WEBHOOK_ACCESS_TOKEN', 'ec2a9c0db08fdda7ca38f346ebf34eb0ab3a8ff918db3a9d9fed2a71f68865a8')

        if not expected_token or expected_token == '':
            return False, f"Webhook access token not configured."

        # Check Authorization header (Bearer token)
        auth_header = request.headers.get('Authorization', '')
        bearer_token = None
        if auth_header.startswith('Bearer '):
            bearer_token = auth_header[7:]  # Remove 'Bearer ' prefix

        # Check X-Access-Token header
        x_access_token = request.headers.get('X-Access-Token', '')

        # Verify both tokens match the expected token
        if bearer_token == expected_token and x_access_token == expected_token:
            return True, None

        # Log the verification failure details
        error_details = []
        if not bearer_token:
            error_details.append("Missing Authorization Bearer token")
        elif bearer_token != expected_token:
            error_details.append("Invalid Authorization Bearer token")

        if not x_access_token:
            error_details.append("Missing X-Access-Token header")
        elif x_access_token != expected_token:
            error_details.append("Invalid X-Access-Token header")

        return False, "; ".join(error_details)

    def generate_input_id(self):
        """Generate random INPUT_ID with 8 characters (lowercase a-z + 0-9)"""
        characters = string.ascii_lowercase + string.digits
        return ''.join(random.choice(characters) for _ in range(8))

    def build_router(self):
 
        @self.app.route('/webhook_input', methods=['POST'])
        def webhook_input():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON"}), 200
            
            s ,a, p, comment = data.get("s"), data.get("a"), data.get("p"), data.get("c")
            rows ,vsl, id    = data.get("rows"), data.get("sl"), data.get("id")
            
            if not s or not a or not p:
                self.util.add_status_frame(f"❌ Webhook failed: Missing symbol or action or price - s:{s} a:{a} p:{p} c:{comment}")
                return jsonify({"error": True, "message": "Missing symbol or action or price"}), 200

            self.util.add_status_frame(f"✅ Webhook Input: s:{s} a:{a} p:{p} c:{comment} :: {data}")

            # Collect TP and Lot values

            symbol = s + self.config.symbol_posfix.get()
            price = mt5.symbol_info_tick(symbol).ask if a == "Buy Now" else mt5.symbol_info_tick(symbol).bid if a == "Sell Now" else p

            # Generate unique INPUT_ID for this order group
            # input_id = self.generate_input_id()
            # base_comment = f"{comment}_{input_id}" 

            i = 0
            for row in rows:
                i+=1
                tp = float(row['tp'])
                lot = float(row['lot'])
                # print(f"TP{i} {tp} lot {lot} A {a} S {symbol} P {price} SL {vsl} C {comment}")
                if tp and lot:
                    self.util.send_order(a, symbol, lot, price, vsl, tp, comment + f"_TP{i}_{id}")

            # Log the generated INPUT_ID for reference
            self.util.add_status_frame(f"✅ Webhook Input orders sent with ID: {id}", "green")

            return jsonify({"error": False, "message": "Success"}), 200
 
        @self.app.route('/webhook_instant', methods=['POST'])
        def webhook_instant():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON"}), 200
            
            s ,a, p,     = data.get("s"), data.get("a"), data.get("p")
            comment, lot = data.get("c"), data.get("lot")
            ptp ,psl     =  data.get("ptp"),  data.get("psl")
            vtp ,vsl     =  data.get("tp"),  data.get("sl")

            if not s or not a:
                self.util.add_status_frame(f"❌ Webhook failed: Missing symbol or action or price - s:{s} a:{a} c:{comment}")
                return jsonify({"error": True, "message": "Missing symbol or action or price"}), 200
            
            self.util.add_status_frame(f"✅ Webhook Instant: s:{s} a:{a} c:{comment} :: {data}")

            # symbol = self.config.symbols[s] + self.config.symbol_posfix.get()
            symbol = s + self.config.symbol_posfix.get()
            lot = self.lot_var.get() if not lot else lot
            point = mt5.symbol_info(symbol).point
            
            entry = mt5.symbol_info_tick(symbol).ask if a == "Buy Now" else (mt5.symbol_info_tick(symbol).bid if a == "Sell Now" else p )
            if (a in ["Buy Limit", "Buy Now", "Buy Stop"]) and (self.side_var.get() == "BUY" or self.side_var.get() == "BOTH"):
                    tp = (entry + (point*ptp)) if not vtp else vtp
                    sl = (entry - (point*psl)) if not vsl else vsl
                    # tp = (entry + (stp*xtp)) if not vtp else vtp
                    # sl = (entry - (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, entry, sl, tp, comment)
                    return jsonify({"error": False, "message": "Success"}), 200
            elif (a in ["Sell Limit", "Sell Now", "Sell Stop"]) and (self.side_var.get() == "Sell" or self.side_var.get() == "BOTH"):
                    tp = (entry - (point*ptp)) if not vtp else vtp
                    sl = (entry + (point*psl)) if not vsl else vsl
                    # tp = (entry - (stp*xtp)) if not vtp else vtp
                    # sl = (entry + (stp*xsl)) if not vsl else vsl
                    self.util.send_order(a, symbol, lot, entry, sl, tp, comment)
                    return jsonify({"error": False, "message": "Success"}), 200
                     
            return jsonify({"error": True, "message": "Only accept " + self.side_var.get()}), 200 
        
        @self.app.route('/webhook_control', methods=['POST'])
        def webhook_control():
            if not self.webhook_enabled:
                self.util.add_status_frame(f"❌ Webhook failed: Webhook disabled - " + request.get_data(as_text=True))
                # return "Webhook disabled", 403
                return "Webhook disabled", 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON "}), 400
            
            s ,a, condition = data.get("s"), data.get("a"), data.get("c")
            filter_comment = data.get("filter", "")  # Optional filter parameter
            symbol = s + self.config.symbol_posfix.get()

            if a == "close":
                # condition = all, all-buy, all-sell | all-profit, buy-profit, sell-profit | all-loss, buy-loss, sell-loss
                self.util.close_orders_by_condition(symbol, condition)
            elif a == "close-pending":
                # Close pending orders (limit and stop orders) that are not yet active
                # filter_comment: if empty, closes all pending orders; if provided, closes orders where comment ends with filter_comment
                closed_count = self.util.close_pending_orders_by_filter(symbol, filter_comment)
                self.util.add_status_frame(f"✅ Webhook Control: Cancelled {closed_count} pending orders for {symbol} with filter '{filter_comment}'")
            elif a == "sl2be":
                # condition = all, all-buy, all-sell
                self.util.update_SL_to_BE_by_condition(symbol, condition)
            elif a == "tp2be":
                # condition = all, all-buy, all-sell
                self.util.update_TP_to_BE_by_condition(symbol, condition)

            # return "OK", 200
            return jsonify({"error": False, "message": "Success"}), 200

        @self.app.route('/webhook_orders_data', methods=['GET'])
        def webhook_orders_data():
            """Get orders data for web app display"""
            if not self.webhook_enabled:
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            try:
                # Get positions data
                positions_data = []
                positions = mt5.positions_get() if mt5.initialize() else []
                if positions:
                    for pos in positions:
                        positions_data.append({
                            "ticket": pos.ticket,
                            "symbol": pos.symbol,
                            "type": "BUY" if pos.type == 0 else "SELL",
                            "volume": pos.volume,
                            "entry": pos.price_open,
                            "current": pos.price_current,
                            "sl": pos.sl if pos.sl > 0 else None,
                            "tp": pos.tp if pos.tp > 0 else None,
                            "profit": pos.profit,
                            "comment": pos.comment
                        })

                # Get pending orders data
                pending_data = []
                orders = mt5.orders_get() if mt5.initialize() else []
                if orders:
                    for order in orders:
                        order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                         "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                         "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                         "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                         f"Type {order.type}"

                        pending_data.append({
                            "ticket": order.ticket,
                            "symbol": order.symbol,
                            "type": order_type_name,
                            "volume": order.volume_initial,
                            "entry": order.price_open,
                            "sl": order.sl if order.sl > 0 else None,
                            "tp": order.tp if order.tp > 0 else None,
                            "comment": order.comment
                        })

                # Get grouped data
                sig_groups = self.get_grouped_orders("SIG")
                input_groups = self.get_grouped_orders("INPUT")

                return jsonify({
                    "error": False,
                    "data": {
                        "positions": positions_data,
                        "pending": pending_data,
                        "sig_groups": sig_groups,
                        "input_groups": input_groups,
                        "timestamp": time.time()
                    }
                }), 200

            except Exception as e:
                self.util.add_status_frame(f"❌ Orders data error: {e}", "red")
                return jsonify({"error": True, "message": f"Data retrieval error: {str(e)}"}), 500

        @self.app.route('/webhook_orders_action', methods=['POST'])
        def webhook_orders_action():
            """Handle order actions from web app"""
            if not self.webhook_enabled:
                return jsonify({"error": True, "message": "Webhook disabled"}), 200

            # Verify access token
            is_valid, error_message = self.verify_access_token()
            if not is_valid:
                self.util.add_status_frame(f"❌ Webhook failed: Access token verification failed - {error_message}")
                return jsonify({"error": True, "message": f"Access denied: {error_message}"}), 401

            data = request.get_json(silent=True, force=True)
            if not data:
                self.util.add_status_frame(f"❌ Webhook failed: Invalid data, not JSON - " + request.get_data(as_text=True))
                return jsonify({"error": True, "message": "Invalid data, not JSON"}), 400

            action = data.get("action")
            group_type = data.get("group_type")  # "SIG" or "INPUT"
            group_id = data.get("group_id")
            auto_be_settings = data.get("auto_be_settings")  # For toggle auto SL to BE

            try:
                if action == "close_group":
                    if group_type == "SIG" and group_id:
                        closed_count = self.close_group_by_id(group_id, "SIG")
                        self.util.add_status_frame(f"✅ Webhook: Closed SIG group {group_id}: {closed_count} orders")
                        return jsonify({"error": False, "message": f"Closed SIG group {group_id}: {closed_count} orders"}), 200
                    elif group_type == "INPUT" and group_id:
                        closed_count = self.close_group_by_id(group_id, "INPUT")
                        self.util.add_status_frame(f"✅ Webhook: Closed INPUT group {group_id}: {closed_count} orders")
                        return jsonify({"error": False, "message": f"Closed INPUT group {group_id}: {closed_count} orders"}), 200
                    else:
                        return jsonify({"error": True, "message": "Invalid group_type or missing group_id"}), 400

                elif action == "toggle_auto_be":
                    if auto_be_settings:
                        # Update auto SL to BE settings
                        result = self.update_auto_be_settings(auto_be_settings)
                        return jsonify({"error": False, "message": "Auto SL to BE settings updated", "result": result}), 200
                    else:
                        return jsonify({"error": True, "message": "Missing auto_be_settings"}), 400

                else:
                    return jsonify({"error": True, "message": f"Unknown action: {action}"}), 400

            except Exception as e:
                self.util.add_status_frame(f"❌ Orders action error: {e}", "red")
                return jsonify({"error": True, "message": f"Action error: {str(e)}"}), 500

    def extract_sig_id(self, comment):
        """Extract SIG_ID from comment like 'SIG_XXX_X_X_<SIG_ID>'"""
        if not comment.startswith('SIG'):
            return None
        parts = comment.split('_')
        if len(parts) >= 5:
            return parts[-1]
        return None

    def extract_input_id(self, comment):
        """Extract INPUT_ID from comment like 'INPUT_MANUAL_<INPUT_ID>'"""
        if not comment.startswith('INPUT'):
            return None
        parts = comment.split('_')
        if len(parts) >= 3:
            for part in parts:
                if len(part) == 8 and part.islower() and any(c.isdigit() for c in part):
                    return part
        return None

    def get_grouped_orders(self, group_type):
        """Get grouped orders data for SIG or INPUT"""
        from collections import defaultdict

        groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if group_type == "SIG":
                    group_id = self.extract_sig_id(pos.comment)
                else:  # INPUT
                    group_id = self.extract_input_id(pos.comment)

                if group_id:
                    groups[group_id]['positions'].append({
                        "ticket": pos.ticket,
                        "symbol": pos.symbol,
                        "type": "BUY" if pos.type == 0 else "SELL",
                        "volume": pos.volume,
                        "entry": pos.price_open,
                        "current": pos.price_current,
                        "sl": pos.sl if pos.sl > 0 else None,
                        "tp": pos.tp if pos.tp > 0 else None,
                        "profit": pos.profit,
                        "comment": pos.comment
                    })
                    groups[group_id]['total_volume'] += pos.volume
                    groups[group_id]['total_profit'] += pos.profit
                    if '_TP' in pos.comment:
                        groups[group_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if group_type == "SIG":
                    group_id = self.extract_sig_id(order.comment)
                else:  # INPUT
                    group_id = self.extract_input_id(order.comment)

                if group_id:
                    order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                     "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                     "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                     "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                     f"Type {order.type}"

                    groups[group_id]['pending'].append({
                        "ticket": order.ticket,
                        "symbol": order.symbol,
                        "type": order_type_name,
                        "volume": order.volume_initial,
                        "entry": order.price_open,
                        "sl": order.sl if order.sl > 0 else None,
                        "tp": order.tp if order.tp > 0 else None,
                        "comment": order.comment
                    })
                    groups[group_id]['total_volume'] += order.volume_initial
                    if '_TP' in order.comment:
                        groups[group_id]['tp_count'] += 1

        # Convert to list format
        result = []
        for group_id, data in groups.items():
            result.append({
                "group_id": group_id,
                "tp_count": data['tp_count'],
                "total_volume": data['total_volume'],
                "total_profit": data['total_profit'],
                "positions_count": len(data['positions']),
                "pending_count": len(data['pending']),
                "positions": data['positions'],
                "pending": data['pending']
            })

        return result

    def close_group_by_id(self, group_id, group_type):
        """Close all orders in a group by ID"""
        closed_count = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                should_close = False
                if group_type == "SIG" and pos.comment.endswith(group_id):
                    should_close = True
                elif group_type == "INPUT" and group_id in pos.comment and pos.comment.startswith('INPUT'):
                    should_close = True

                if should_close:
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close {group_type} group {group_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_count += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                should_close = False
                if group_type == "SIG" and order.comment.endswith(group_id):
                    should_close = True
                elif group_type == "INPUT" and group_id in order.comment and order.comment.startswith('INPUT'):
                    should_close = True

                if should_close:
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_count += 1

        return closed_count

    def update_auto_be_settings(self, settings):
        """Update auto SL to BE settings (placeholder for future integration)"""
        # This would integrate with TabOrders auto BE settings
        # For now, just return the settings received
        self.util.add_status_frame(f"🔄 Webhook: Auto SL to BE settings updated: {settings}")
        return {
            "updated": True,
            "settings": settings,
            "message": "Auto SL to BE settings updated via webhook"
        }
        