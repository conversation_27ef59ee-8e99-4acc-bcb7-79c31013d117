2025-07-03 10:33:24,739 [INFO] Serving on http://0.0.0.0:5000
2025-07-03 10:33:25,236 [INFO] \U0001f7e2 Webhook server STARTED on port 5000
2025-07-03 10:33:25,665 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 10:33:25,667 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 10:33:25,827 [INFO] \U0001f504 Auto BE (All): Updated 2 orders for XAUUSD.iux
2025-07-03 10:33:55,942 [INFO] \U0001f504 Auto BE (All): Updated 2 orders for XAUUSD.iux
2025-07-03 10:34:25,996 [INFO] \U0001f504 Auto BE (All): Updated 2 orders for XAUUSD.iux
2025-07-03 10:34:56,055 [INFO] \U0001f504 Auto BE (All): Updated 2 orders for XAUUSD.iux
2025-07-03 10:35:26,116 [INFO] \U0001f504 Auto BE (All): Updated 2 orders for XAUUSD.iux
2025-07-03 10:35:28,142 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 10:35:28,168 [INFO] Serving on http://0.0.0.0:5001
2025-07-03 10:35:28,668 [INFO] \U0001f7e2 Webhook server STARTED on port 5001
2025-07-03 10:35:32,027 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 10:35:32,042 [INFO] Serving on http://0.0.0.0:5002
2025-07-03 10:35:32,553 [INFO] \U0001f7e2 Webhook server STARTED on port 5002
2025-07-03 10:35:34,606 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 10:35:41,068 [INFO] Serving on http://0.0.0.0:5003
2025-07-03 10:35:41,577 [INFO] \U0001f7e2 Webhook server STARTED on port 5003
2025-07-03 10:35:49,405 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 10:35:49,405 [INFO] \U0001f534 Webhook server stopped
2025-07-03 10:35:49,406 [INFO] \U0001f534 Stopping background processes...
2025-07-03 10:35:51,413 [INFO] \U0001f534 MT5 connection closed
2025-07-03 13:25:45,673 [INFO] Serving on http://0.0.0.0:5050
2025-07-03 13:25:46,185 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-07-03 13:25:46,595 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 13:25:46,612 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 13:25:53,217 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 13:25:53,217 [INFO] \U0001f534 Webhook server stopped
2025-07-03 13:25:53,217 [INFO] \U0001f534 Stopping background processes...
2025-07-03 13:25:55,220 [INFO] \U0001f534 MT5 connection closed
2025-07-03 19:21:14,835 [INFO] Serving on http://0.0.0.0:5050
2025-07-03 19:21:15,335 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-07-03 19:21:15,817 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 19:21:15,846 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 19:32:33,415 [INFO] Serving on http://0.0.0.0:5050
2025-07-03 19:32:33,923 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-07-03 19:32:34,261 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 19:32:34,277 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 19:39:54,687 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:TEST_Comment :: {'a': 'Buy Now', 'ptp': 2000, 'psl': 1000, 'lot': 0.01, 's': 'XAUUSD', 'c': 'TEST_Comment', '_meta': {'source': 'web_app', 'group': 'g_instant', 'timestamp': '2025-07-03T12:29:59.667Z', 'accessToken': 'provided'}}
2025-07-03 19:39:54,786 [INFO] \u274c Execute failed: No return value
2025-07-03 19:43:59,280 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 19:43:59,280 [INFO] \U0001f534 Webhook server stopped
2025-07-03 19:43:59,280 [INFO] \U0001f534 Stopping background processes...
2025-07-03 19:44:01,281 [INFO] \U0001f534 MT5 connection closed
2025-07-03 19:44:19,545 [INFO] Serving on http://0.0.0.0:5050
2025-07-03 19:44:20,044 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-07-03 19:44:20,582 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 19:44:20,612 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 19:44:29,063 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3329.5 c:Test_C_Hummy :: {'rows': [{'tp': 3335, 'lot': 0.01}, {'tp': 3339, 'lot': 0.01}, {'tp': 3345, 'lot': 0.01}, {'tp': 3358, 'lot': 0.01}, {'tp': 3367, 'lot': 0.01}], 'a': 'Buy Limit', 'p': 3329.5, 'sl': 3323.5, 's': 'XAUUSD', 'c': 'Test_C_Hummy'}
2025-07-03 19:44:29,201 [INFO] \u274c Execute failed: No return value
2025-07-03 19:44:29,271 [INFO] \u274c Execute failed: No return value
2025-07-03 19:44:29,334 [INFO] \u274c Execute failed: No return value
2025-07-03 19:44:29,401 [INFO] \u274c Execute failed: No return value
2025-07-03 19:44:29,469 [INFO] \u274c Execute failed: No return value
2025-07-03 19:44:29,469 [INFO] \u2705 Webhook Input: 0/5 orders sent with ID: None
2025-07-03 19:44:50,963 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:TEST_Comment :: {'a': 'Buy Now', 'ptp': 2000, 'psl': 1000, 'lot': 0.01, 's': 'XAUUSD', 'c': 'TEST_Comment', '_meta': {'source': 'web_app', 'group': 'g_instant', 'timestamp': '2025-07-03T12:29:59.667Z', 'accessToken': 'provided'}}
2025-07-03 19:44:51,044 [INFO] \u274c Execute failed: No return value
2025-07-03 19:53:17,750 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:TEST_Comment :: {'a': 'Buy Now', 'ptp': 2000, 'psl': 1000, 'lot': 0.01, 's': 'XAUUSD', 'c': 'TEST_Comment', '_meta': {'source': 'web_app', 'group': 'g_instant', 'timestamp': '2025-07-03T12:29:59.667Z', 'accessToken': 'provided'}}
2025-07-03 19:53:17,828 [INFO] \u274c Execute failed: No return value
2025-07-03 19:53:35,589 [INFO] \U0001f534 Webhook server STOPPED
2025-07-03 19:53:35,589 [INFO] \U0001f534 Webhook server stopped
2025-07-03 19:53:35,589 [INFO] \U0001f534 Stopping background processes...
2025-07-03 19:53:37,595 [INFO] \U0001f534 MT5 connection closed
2025-07-03 19:53:47,128 [INFO] Serving on http://0.0.0.0:5050
2025-07-03 19:53:47,635 [INFO] \U0001f7e2 Webhook server STARTED on port 5050
2025-07-03 19:53:48,062 [INFO] \U0001f7e2 Orders auto-refresh started
2025-07-03 19:53:48,077 [INFO] \U0001f4cb TabOrders initialized - Auto BE enabled, auto refresh disabled
2025-07-03 19:54:01,900 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:TEST_Comment :: {'a': 'Buy Now', 'ptp': 2000, 'psl': 1000, 'lot': 0.01, 's': 'XAUUSD', 'c': 'TEST_Comment', '_meta': {'source': 'web_app', 'group': 'g_instant', 'timestamp': '2025-07-03T12:29:59.667Z', 'accessToken': 'provided'}}
2025-07-03 19:54:01,978 [INFO] \U0001f504 Preparing order: Buy Now XAUUSD.iux 0.01 lots
2025-07-03 19:54:02,078 [INFO] \U0001f4ca Price info: bid=3319.63, ask=3319.77, using=3319.63
2025-07-03 19:54:02,117 [INFO] \U0001f4cb Order request prepared: {'action': 1, 'symbol': 'XAUUSD.iux', 'volume': 0.01, 'type': 0, 'price': 3319.63, 'sl': 3309.77, 'tp': 3339.77, 'deviation': 10, 'magic': 161032, 'comment': 'TEST_Comment', 'type_filling': 0}
2025-07-03 19:54:02,169 [INFO] \u274c MT5 operation failed: (-2, 'Unnamed arguments not allowed')
2025-07-03 19:54:02,174 [INFO] \U0001f4e4 Order send result: None
2025-07-03 19:54:02,213 [INFO] \u274c Execute failed: No return value
