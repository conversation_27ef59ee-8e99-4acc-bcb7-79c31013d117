#!/usr/bin/env python3
"""
Quick diagnostic script for send_order issues
Run this to identify why send_order is returning None
"""

import MetaTrader5 as mt5
import sys
import os

# Add the App directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'App'))

def quick_mt5_test():
    """Quick MT5 connection and basic operation test"""
    print("=== Quick MT5 Diagnostic ===")
    
    # Test 1: Initialize MT5
    print("1. Testing MT5 initialization...")
    if not mt5.initialize():
        error = mt5.last_error()
        print(f"❌ FAILED: MT5 initialization failed: {error}")
        print("   Solution: Make sure MetaTrader 5 is running and logged in")
        return False
    print("✅ PASSED: MT5 initialized")
    
    # Test 2: Account info
    print("2. Testing account access...")
    account = mt5.account_info()
    if not account:
        print("❌ FAILED: Cannot get account info")
        print("   Solution: Make sure you're logged into a trading account")
        return False
    print(f"✅ PASSED: Account {account.login}, Balance: {account.balance}")
    
    # Test 3: Symbol access
    print("3. Testing symbol access...")
    symbol = "EURUSD"
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        print(f"❌ FAILED: Cannot get {symbol} info")
        print("   Solution: Make sure EURUSD is available in Market Watch")
        return False
    print(f"✅ PASSED: {symbol} info available")
    
    # Test 4: Tick data
    print("4. Testing tick data...")
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        print(f"❌ FAILED: Cannot get {symbol} tick data")
        print("   Solution: Check market hours and symbol availability")
        return False
    print(f"✅ PASSED: Tick data - Bid: {tick.bid}, Ask: {tick.ask}")
    
    # Test 5: Trading permissions
    print("5. Testing trading permissions...")
    if not account.trade_allowed:
        print("❌ FAILED: Trading not allowed on this account")
        print("   Solution: Check account settings and broker permissions")
        return False
    print("✅ PASSED: Trading is allowed")
    
    # Test 6: Market status
    print("6. Testing market status...")
    if not symbol_info.trade_mode:
        print(f"❌ WARNING: {symbol} trading may be restricted")
        print("   Note: Check market hours and trading sessions")
    else:
        print(f"✅ PASSED: {symbol} trading is available")
    
    return True

def test_order_request():
    """Test order request creation"""
    print("\n=== Testing Order Request Creation ===")
    
    try:
        from config import Config
        from util import Util
        
        config = Config()
        util = Util(config)
        
        # Test parameters
        order_type = "Buy Now"
        symbol = "EURUSD"
        lot = 0.01
        
        print(f"Testing order: {order_type} {symbol} {lot} lots")
        
        # Get current price
        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            print("❌ FAILED: Cannot get current price")
            return False
        
        entry = tick.ask
        sl = entry - 0.0050  # 50 pips SL
        tp = entry + 0.0050  # 50 pips TP
        
        print(f"Prices: Entry={entry}, SL={sl}, TP={tp}")
        
        # Test the send_order method with detailed output
        print("\nCalling send_order with debug output...")
        result = util.send_order(order_type, symbol, lot, entry, sl, tp, "TEST_ORDER")
        
        print(f"\nResult: {result}")
        
        if result is None:
            print("❌ ISSUE FOUND: send_order returned None")
            print("   Check the status messages above for the specific error")
            return False
        else:
            print("✅ send_order returned a result")
            if hasattr(result, 'retcode'):
                print(f"   Return code: {result.retcode}")
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    print("   ✅ Order would be successful")
                else:
                    print(f"   ❌ Order would fail: {result.comment}")
            return True
            
    except Exception as e:
        print(f"❌ FAILED: Exception in test: {e}")
        return False

def main():
    print("Send Order Diagnostic Tool")
    print("=" * 40)
    
    # Quick MT5 test
    if not quick_mt5_test():
        print("\n❌ MT5 basic tests failed. Fix these issues first.")
        input("Press Enter to exit...")
        return
    
    print("\n✅ MT5 basic tests passed!")
    
    # Test order request
    if test_order_request():
        print("\n✅ Order request test passed!")
        print("\nIf you're still getting None from send_order:")
        print("1. Check the detailed status messages in your app")
        print("2. Make sure MT5 is connected and trading is allowed")
        print("3. Verify the symbol is available and market is open")
    else:
        print("\n❌ Order request test failed!")
        print("Check the error messages above for specific issues.")
    
    print("\n" + "=" * 40)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
