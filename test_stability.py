#!/usr/bin/env python3
"""
Test script to verify application stability improvements
"""

import time
import threading
import gc
import psutil
import os
from datetime import datetime

def monitor_memory_usage(duration_minutes=60):
    """Monitor memory usage for specified duration"""
    print(f"Starting memory monitoring for {duration_minutes} minutes...")
    
    process = psutil.Process(os.getpid())
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    max_memory = 0
    memory_readings = []
    
    while time.time() < end_time:
        try:
            # Get memory info
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # Convert to MB
            
            memory_readings.append({
                'time': datetime.now().strftime('%H:%M:%S'),
                'memory_mb': memory_mb,
                'threads': threading.active_count()
            })
            
            if memory_mb > max_memory:
                max_memory = memory_mb
            
            # Print status every 5 minutes
            elapsed = (time.time() - start_time) / 60
            if int(elapsed) % 5 == 0 and elapsed > 0:
                print(f"[{elapsed:.0f}min] Memory: {memory_mb:.1f}MB, Threads: {threading.active_count()}, Max: {max_memory:.1f}MB")
            
            time.sleep(60)  # Check every minute
            
        except Exception as e:
            print(f"Monitoring error: {e}")
            break
    
    # Summary
    print(f"\n=== Memory Monitoring Summary ===")
    print(f"Duration: {duration_minutes} minutes")
    print(f"Max Memory: {max_memory:.1f}MB")
    print(f"Final Memory: {memory_readings[-1]['memory_mb']:.1f}MB" if memory_readings else "No readings")
    print(f"Final Threads: {memory_readings[-1]['threads']}" if memory_readings else "No readings")
    
    return memory_readings

def test_thread_safety():
    """Test thread safety of GUI updates"""
    print("Testing thread safety...")
    
    # Simulate multiple threads updating status
    def worker(worker_id):
        for i in range(10):
            print(f"Worker {worker_id}: Update {i}")
            time.sleep(0.1)
    
    threads = []
    for i in range(5):
        t = threading.Thread(target=worker, args=(i,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print("Thread safety test completed")

def test_memory_cleanup():
    """Test memory cleanup functionality"""
    print("Testing memory cleanup...")
    
    # Create some objects to cleanup
    test_objects = []
    for i in range(1000):
        test_objects.append(f"Test object {i}" * 100)
    
    print(f"Created {len(test_objects)} test objects")
    
    # Clear references
    test_objects.clear()
    
    # Force garbage collection
    collected = gc.collect()
    print(f"Garbage collector freed {collected} objects")
    
    print("Memory cleanup test completed")

if __name__ == "__main__":
    print("=== Application Stability Test ===")
    print(f"Start time: {datetime.now()}")
    print(f"Python PID: {os.getpid()}")
    
    # Run tests
    test_thread_safety()
    test_memory_cleanup()
    
    # Ask user if they want to run memory monitoring
    response = input("\nRun memory monitoring for 10 minutes? (y/n): ")
    if response.lower() == 'y':
        monitor_memory_usage(10)
    
    print("\n=== Test Completed ===")
