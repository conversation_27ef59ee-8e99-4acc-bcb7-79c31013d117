python -m pip install --upgrade pip setuptools wheel virtualenv

- ดาวน์โหลดและติดตั้ง **Visual Studio Build Tools** จาก [Visual Studio Download](https://visualstudio.microsoft.com/visual-cpp-build-tools/). (vs_BuildTools.exe)
- ระหว่างการติดตั้ง ให้เลือกส่วนประกอบต่อไปนี้:
    - **MSVC v142 - VS 2019 C++ x64/x86 build tools**
    - **Windows 11 SDK** (ขึ้นอยู่กับระบบของคุณ)
    - **C++ CMake tools for Windows**

- กด Start > พิมพ์ PowerShell > คลิกขวา > Run as Administrator
Set-ExecutionPolicy RemoteSigned

cd /project/path
virtualenv venv
venv\Scripts\activate

==============================================

- Option 1 หากมี requirements.txt อยู่แล้ว
pip install -r requirements.txt

==============================================

- Option 2 ติดตั้งใหม่ตั้งหมด
pip install MetaTrader5  
pip install --upgrade MetaTrader5

- ดาวน์โหลด https://github.com/ta-lib/ta-lib-python และรันคำสั่ง
python -m pip install Backup\ta_lib-0.6.3-cp310-cp310-win_amd64.whl
pip install pandas psutil python-dotenv tk customtkinter matplotlib pystray pillow objgraph flask waitress requests 
pip install --upgrade google-api-python-client google-auth-httplib2 google-auth-oauthlib

==============================================

- สร้าง requirements list เพื่อให้ติดตั้งที่เครื่องอื่นสะดวกขึ้น
pip freeze > requirements.txt 

=================================

 

See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
  df_AHTF['emaTrend'] = emaTrend #ta.EMA(df_AHTF['close'], timeperiod=emaTrend)
📉 Short Retest Signal M5_E1 @ 2025-04-28 08:40:00
C:\Users\<USER>\python\App\tab_auto.py:257: SettingWithCopyWarning:
A value is trying to be set on a copy of a slice from a DataFrame.
Try using .loc[row_indexer,col_indexer] = value instead
 