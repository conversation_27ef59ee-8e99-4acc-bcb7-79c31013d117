# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from google import genai
from google.genai import types


def generate():
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.0-flash"
    # model = "gemini-2.5-pro-preview-05-06"
    contents = [
        types.Content(
            role="user",
            parts=[
                # types.Part.from_text(text="""INSERT_INPUT_HERE"""),
                types.Part.from_text(text="ลิมิตการทดสอบ gemini api flash model ได้แค่ไหน ตอบสั้นๆ"),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        response_mime_type="text/plain",
    )

    # for chunk in client.models.generate_content_stream(
    #     model=model,
    #     contents=contents,
    #     config=generate_content_config,
    # ):
    #     print(chunk.text, end="")
    print(client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    ).text,end="")

if __name__ == "__main__":
    generate()
