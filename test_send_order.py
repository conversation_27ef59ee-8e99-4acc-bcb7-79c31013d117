#!/usr/bin/env python3
"""
Test script to debug send_order issues
"""

import MetaTrader5 as mt5
import sys
import os

# Add the App directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'App'))

from config import Config
from util import Util

def test_mt5_connection():
    """Test basic MT5 connection"""
    print("=== Testing MT5 Connection ===")
    
    # Initialize MT5
    if not mt5.initialize():
        error = mt5.last_error()
        print(f"❌ MT5 initialization failed: {error}")
        return False
    
    print("✅ MT5 initialized successfully")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info:
        print(f"✅ Account: {account_info.login}, Balance: {account_info.balance}")
    else:
        print("❌ Failed to get account info")
        return False
    
    # Test symbol info
    symbol = "EURUSD"
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info:
        print(f"✅ Symbol {symbol} info retrieved")
    else:
        print(f"❌ Failed to get {symbol} info")
        return False
    
    # Test tick info
    tick_info = mt5.symbol_info_tick(symbol)
    if tick_info:
        print(f"✅ Tick info: bid={tick_info.bid}, ask={tick_info.ask}")
    else:
        print(f"❌ Failed to get {symbol} tick info")
        return False
    
    return True

def test_config_mappings():
    """Test config mappings"""
    print("\n=== Testing Config Mappings ===")
    
    config = Config()
    
    # Test order type mappings
    order_types = ["Buy Now", "Sell Now", "Buy Limit", "Sell Limit", "Buy Stop", "Sell Stop"]
    
    for order_type in order_types:
        if order_type in config.order_type_mapping:
            print(f"✅ Order type '{order_type}': {config.order_type_mapping[order_type]}")
        else:
            print(f"❌ Missing order type mapping: {order_type}")
    
    for order_type in order_types:
        if order_type in config.action_type_mapping:
            print(f"✅ Action type '{order_type}': {config.action_type_mapping[order_type]}")
        else:
            print(f"❌ Missing action type mapping: {order_type}")

def test_send_order_preparation():
    """Test send_order method step by step"""
    print("\n=== Testing Send Order Preparation ===")
    
    # Create config and util
    config = Config()
    util = Util(config)
    
    # Test parameters
    order_type = "Buy Now"
    symbol = "EURUSD"
    lot = 0.01
    entry = 1.1000
    sl = 1.0900
    tp = 1.1100
    comment = "Test Order"
    
    print(f"Testing with: {order_type} {symbol} {lot} lots")
    
    # Test step by step
    try:
        # Step 1: Get tick info
        print("Step 1: Getting tick info...")
        tick_info = util.safe_mt5_call(mt5.symbol_info_tick, symbol)
        if tick_info:
            print(f"✅ Tick info: bid={tick_info.bid}, ask={tick_info.ask}")
        else:
            print("❌ Failed to get tick info")
            return False
        
        # Step 2: Calculate price
        print("Step 2: Calculating price...")
        price = tick_info.bid if order_type == "Buy Now" else (tick_info.ask if order_type == "Sell Now" else entry)
        print(f"✅ Price calculated: {price}")
        
        # Step 3: Check mappings
        print("Step 3: Checking mappings...")
        if order_type in config.action_type_mapping:
            action = config.action_type_mapping[order_type]
            print(f"✅ Action mapping: {action}")
        else:
            print(f"❌ Missing action mapping for {order_type}")
            return False
        
        if order_type in config.order_type_mapping:
            order_type_mt5 = config.order_type_mapping[order_type]
            print(f"✅ Order type mapping: {order_type_mt5}")
        else:
            print(f"❌ Missing order type mapping for {order_type}")
            return False
        
        # Step 4: Create request
        print("Step 4: Creating request...")
        request = {
            "action": action,
            "symbol": symbol,
            "volume": float(lot),
            "type": order_type_mt5,
            "price": float(price),
            "sl": float(sl),
            "tp": float(tp),
            "deviation": 10,
            "magic": 161032,
            "comment": comment,
            "type_filling": mt5.ORDER_FILLING_FOK,
        }
        print(f"✅ Request created: {request}")
        
        # Step 5: Test order send (dry run - don't actually send)
        print("Step 5: Testing order send (dry run)...")
        print("⚠️ Skipping actual order send for safety")
        print("✅ Order preparation successful!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in send_order preparation: {e}")
        return False

def test_actual_send_order():
    """Test actual send_order method"""
    print("\n=== Testing Actual Send Order Method ===")
    
    # Create config and util
    config = Config()
    util = Util(config)
    
    # Test parameters (very small lot for safety)
    order_type = "Buy Now"
    symbol = "EURUSD"
    lot = 0.01  # Very small lot
    entry = 1.1000
    sl = 1.0900
    tp = 1.1100
    comment = "Test Order"
    
    print(f"⚠️ WARNING: This will attempt to place a real order!")
    print(f"Order: {order_type} {symbol} {lot} lots")
    
    response = input("Do you want to proceed? (y/N): ")
    if response.lower() != 'y':
        print("Test cancelled by user")
        return False
    
    # Call send_order
    result = util.send_order(order_type, symbol, lot, entry, sl, tp, comment)
    
    if result is None:
        print("❌ send_order returned None")
        return False
    else:
        print(f"✅ send_order returned: {result}")
        if hasattr(result, 'retcode'):
            print(f"Return code: {result.retcode}")
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print("✅ Order executed successfully!")
            else:
                print(f"❌ Order failed: {result.comment}")
        return True

if __name__ == "__main__":
    print("=== Send Order Debug Test ===")
    
    # Test 1: MT5 Connection
    if not test_mt5_connection():
        print("❌ MT5 connection test failed")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Test 2: Config mappings
    test_config_mappings()
    
    # Test 3: Send order preparation
    if not test_send_order_preparation():
        print("❌ Send order preparation test failed")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Test 4: Actual send order (optional)
    print("\n" + "="*50)
    response = input("Test actual send_order method? (y/N): ")
    if response.lower() == 'y':
        test_actual_send_order()
    
    print("\n=== Test Complete ===")
    input("Press Enter to exit...")
