#!/usr/bin/env python3
"""
Test script for the new webhook orders endpoints
This script demonstrates how to interact with the TabOrders data via webhooks
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
WEBHOOK_BASE_URL = "http://localhost:5000"
ACCESS_TOKEN = os.getenv('WEBHOOK_ACCESS_TOKEN', 'your_secure_webhook_access_token_here')

def make_authenticated_request(endpoint, method="GET", data=None):
    """Make an authenticated request to the webhook"""
    url = f"{WEBHOOK_BASE_URL}/{endpoint}"
    
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'WebApp-Orders-Client/1.0',
        'Authorization': f'Bearer {ACCESS_TOKEN}',
        'X-Access-Token': ACCESS_TOKEN
    }
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=10)
        else:
            return {"error": True, "message": f"Unsupported method: {method}"}
        
        return {
            'status_code': response.status_code,
            'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
            'success': response.status_code == 200
        }
    except requests.exceptions.RequestException as e:
        return {
            'status_code': None,
            'response': f"Request failed: {str(e)}",
            'success': False
        }

def test_get_orders_data():
    """Test getting orders data"""
    print("=== Testing GET Orders Data ===")
    
    result = make_authenticated_request("webhook_orders_data", "GET")
    print(f"Status: {result['status_code']}")
    
    if result['success'] and isinstance(result['response'], dict):
        data = result['response']['data']
        print(f"✅ Success! Retrieved orders data:")
        print(f"  - Positions: {len(data['positions'])}")
        print(f"  - Pending Orders: {len(data['pending'])}")
        print(f"  - SIG Groups: {len(data['sig_groups'])}")
        print(f"  - INPUT Groups: {len(data['input_groups'])}")
        
        # Show sample SIG groups
        if data['sig_groups']:
            print("\n📊 SIG Groups:")
            for group in data['sig_groups'][:3]:  # Show first 3
                print(f"  - {group['group_id']}: {group['tp_count']} TPs, Vol: {group['total_volume']:.2f}, Profit: {group['total_profit']:.2f}")
        
        # Show sample INPUT groups
        if data['input_groups']:
            print("\n📊 INPUT Groups:")
            for group in data['input_groups'][:3]:  # Show first 3
                print(f"  - {group['group_id']}: {group['tp_count']} TPs, Vol: {group['total_volume']:.2f}, Profit: {group['total_profit']:.2f}")
        
        return data
    else:
        print(f"❌ Failed: {result['response']}")
        return None

def test_close_sig_group(sig_id):
    """Test closing a SIG group"""
    print(f"\n=== Testing Close SIG Group: {sig_id} ===")
    
    data = {
        "action": "close_group",
        "group_type": "SIG",
        "group_id": sig_id
    }
    
    result = make_authenticated_request("webhook_orders_action", "POST", data)
    print(f"Status: {result['status_code']}")
    print(f"Response: {result['response']}")
    
    return result['success']

def test_close_input_group(input_id):
    """Test closing an INPUT group"""
    print(f"\n=== Testing Close INPUT Group: {input_id} ===")
    
    data = {
        "action": "close_group",
        "group_type": "INPUT",
        "group_id": input_id
    }
    
    result = make_authenticated_request("webhook_orders_action", "POST", data)
    print(f"Status: {result['status_code']}")
    print(f"Response: {result['response']}")
    
    return result['success']

def test_toggle_auto_be():
    """Test toggling auto SL to BE settings"""
    print("\n=== Testing Toggle Auto SL to BE ===")
    
    data = {
        "action": "toggle_auto_be",
        "auto_be_settings": {
            "all_enabled": True,
            "sig_enabled": False,
            "input_enabled": True
        }
    }
    
    result = make_authenticated_request("webhook_orders_action", "POST", data)
    print(f"Status: {result['status_code']}")
    print(f"Response: {result['response']}")
    
    return result['success']

def test_webhook_input_with_random_id():
    """Test webhook_input with random INPUT_ID generation"""
    print("\n=== Testing Webhook Input with Random ID ===")
    
    data = {
        "s": "XAUUSD",
        "a": "Buy Limit",
        "p": 2000.50,
        "c": "INPUT_WEBHOOK_TEST",
        "sl": 1950.00,
        "rows": [
            {"tp": 2050.00, "lot": 0.01},
            {"tp": 2100.00, "lot": 0.02}
        ]
    }
    
    result = make_authenticated_request("webhook_input", "POST", data)
    print(f"Status: {result['status_code']}")
    print(f"Response: {result['response']}")
    
    return result['success']

def display_orders_summary(orders_data):
    """Display a summary of orders data"""
    if not orders_data:
        return
    
    print("\n" + "="*60)
    print("📊 ORDERS SUMMARY")
    print("="*60)
    
    # Positions summary
    positions = orders_data['positions']
    if positions:
        total_profit = sum(pos['profit'] for pos in positions)
        total_volume = sum(pos['volume'] for pos in positions)
        print(f"📈 Open Positions: {len(positions)}")
        print(f"   Total Volume: {total_volume:.2f}")
        print(f"   Total Profit: {total_profit:.2f}")
        
        # Group by symbol
        symbols = {}
        for pos in positions:
            symbol = pos['symbol']
            if symbol not in symbols:
                symbols[symbol] = {'count': 0, 'volume': 0, 'profit': 0}
            symbols[symbol]['count'] += 1
            symbols[symbol]['volume'] += pos['volume']
            symbols[symbol]['profit'] += pos['profit']
        
        print("   By Symbol:")
        for symbol, data in symbols.items():
            print(f"     {symbol}: {data['count']} orders, Vol: {data['volume']:.2f}, P&L: {data['profit']:.2f}")
    
    # Pending orders summary
    pending = orders_data['pending']
    if pending:
        total_pending_volume = sum(order['volume'] for order in pending)
        print(f"\n⏳ Pending Orders: {len(pending)}")
        print(f"   Total Volume: {total_pending_volume:.2f}")
    
    # Groups summary
    sig_groups = orders_data['sig_groups']
    input_groups = orders_data['input_groups']
    
    if sig_groups:
        print(f"\n🎯 SIG Groups: {len(sig_groups)}")
        for group in sig_groups:
            print(f"   {group['group_id']}: {group['positions_count']}P + {group['pending_count']}O, Profit: {group['total_profit']:.2f}")
    
    if input_groups:
        print(f"\n📝 INPUT Groups: {len(input_groups)}")
        for group in input_groups:
            print(f"   {group['group_id']}: {group['positions_count']}P + {group['pending_count']}O, Profit: {group['total_profit']:.2f}")

def main():
    """Main test function"""
    print("Webhook Orders API Test Script")
    print("=" * 50)
    print(f"Base URL: {WEBHOOK_BASE_URL}")
    print(f"Access Token: {'*' * (len(ACCESS_TOKEN) - 4) + ACCESS_TOKEN[-4:] if len(ACCESS_TOKEN) > 4 else 'NOT_SET'}")
    print()
    
    if ACCESS_TOKEN == 'your_secure_webhook_access_token_here':
        print("⚠️  WARNING: Please set a proper WEBHOOK_ACCESS_TOKEN in your .env file")
        return
    
    # Test 1: Get orders data
    orders_data = test_get_orders_data()
    
    if orders_data:
        display_orders_summary(orders_data)
        
        # Test 2: Close group actions (only if groups exist)
        if orders_data['sig_groups']:
            first_sig = orders_data['sig_groups'][0]['group_id']
            print(f"\n🔄 Found SIG group to test: {first_sig}")
            # Uncomment to actually close: test_close_sig_group(first_sig)
            print("   (Close test skipped - uncomment to run)")
        
        if orders_data['input_groups']:
            first_input = orders_data['input_groups'][0]['group_id']
            print(f"\n🔄 Found INPUT group to test: {first_input}")
            # Uncomment to actually close: test_close_input_group(first_input)
            print("   (Close test skipped - uncomment to run)")
    
    # Test 3: Toggle auto BE
    test_toggle_auto_be()
    
    # Test 4: Webhook input with random ID
    test_webhook_input_with_random_id()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print("\nNote: Group closing tests are commented out to prevent accidental closures.")
    print("Uncomment the test_close_*_group() calls to test actual closing functionality.")

if __name__ == "__main__":
    main()
