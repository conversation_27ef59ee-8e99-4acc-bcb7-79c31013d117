#!/usr/bin/env python3
"""
Test script to verify system tray functionality
Run this to test if pys<PERSON><PERSON> and <PERSON><PERSON> are installed correctly
"""

import sys
import time
import threading

def test_tray_dependencies():
    """Test if system tray dependencies are available"""
    print("Testing system tray dependencies...")
    
    try:
        import pystray
        print("✅ pystray is available")
    except ImportError:
        print("❌ pystray not found. Install with: pip install pystray")
        return False
    
    try:
        from PIL import Image, ImageDraw
        print("✅ Pillow (PIL) is available")
    except ImportError:
        print("❌ Pillow not found. Install with: pip install Pillow")
        return False
    
    return True

def create_test_tray():
    """Create a simple test tray icon"""
    try:
        import pystray
        from PIL import Image, ImageDraw
        
        # Create a simple test icon
        image = Image.new('RGB', (64, 64), color='green')
        draw = ImageDraw.Draw(image)
        draw.ellipse([16, 16, 48, 48], fill='white')
        draw.text((28, 28), "T", fill='black')
        
        def on_quit(icon, item):
            print("Tray icon quit")
            icon.stop()
        
        # Create menu
        menu = pystray.Menu(
            pystray.MenuItem("Test Item", lambda: print("Test clicked")),
            pystray.MenuItem("Quit", on_quit)
        )
        
        # Create icon
        icon = pystray.Icon(
            name="Test Tray",
            icon=image,
            title="Test System Tray",
            menu=menu
        )
        
        print("✅ Test tray icon created successfully")
        print("Look for the green test icon in your system tray")
        print("Right-click it to see the menu")
        print("The test will run for 30 seconds...")
        
        # Run for 30 seconds
        def stop_after_delay():
            time.sleep(30)
            print("Test completed - stopping tray icon")
            icon.stop()
        
        stop_thread = threading.Thread(target=stop_after_delay, daemon=True)
        stop_thread.start()
        
        # Run the tray icon
        icon.run()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test tray: {e}")
        return False

def test_webhook_simulation():
    """Simulate webhook server running in background"""
    print("\nTesting background thread simulation...")
    
    def background_worker():
        for i in range(10):
            print(f"Background process running... {i+1}/10")
            time.sleep(2)
        print("✅ Background process completed successfully")
    
    # Start background thread
    bg_thread = threading.Thread(target=background_worker, daemon=True)
    bg_thread.start()
    
    print("Background thread started (simulating webhook server)")
    return True

if __name__ == "__main__":
    print("=== System Tray Test ===")
    print(f"Python version: {sys.version}")
    print()
    
    # Test dependencies
    if not test_tray_dependencies():
        print("\n❌ Dependencies missing. Please install:")
        print("pip install pystray Pillow")
        print("Or run: install_tray_support.bat")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print("\n✅ All dependencies available!")
    
    # Test background simulation
    test_webhook_simulation()
    
    # Ask user if they want to test tray
    response = input("\nTest system tray icon? (y/n): ")
    if response.lower() == 'y':
        print("\nStarting tray test...")
        if create_test_tray():
            print("✅ Tray test completed successfully!")
        else:
            print("❌ Tray test failed")
    
    print("\n=== Test Complete ===")
    print("If all tests passed, your system tray functionality should work!")
    input("Press Enter to exit...")
