2025-06-30 11:06:27,136 [INFO] \U0001f7e2 Orders auto-refresh started
2025-06-30 11:06:27,183 [INFO] Serving on http://0.0.0.0:5000
2025-06-30 11:06:27,809 [INFO] \u274c Data refresh error: main thread is not in main loop
2025-06-30 11:15:23,728 [INFO] \U0001f7e2 Orders auto-refresh started
2025-06-30 11:15:23,738 [INFO] Serving on http://0.0.0.0:5000
2025-06-30 12:47:23,647 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3273 c:SIG_C_Atom :: {'rows': [{'tp': 3282, 'lot': 0.01}, {'tp': 3289.5, 'lot': 0.01}, {'tp': 3294, 'lot': 0.01}, {'tp': 3308, 'lot': 0.01}], 'a': 'Buy Limit', 'p': 3273, 'sl': 3268, 's': 'XAUUSD', 'c': 'SIG_C_Atom', 'id': '36a8998f', '_meta': {'source': 'web_app', 'group': 'g_input', 'timestamp': '2025-06-30T05:47:22.599Z', 'accessToken': 'provided'}}
2025-06-30 13:24:31,232 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3273 c:SIG_C_Atom :: {'rows': [{'tp': 3282, 'lot': 0.01}, {'tp': 3289.5, 'lot': 0.01}, {'tp': 3294, 'lot': 0.01}, {'tp': 3308, 'lot': 0.01}], 'a': 'Buy Limit', 'p': 3273, 'sl': 3268, 's': 'XAUUSD', 'c': 'SIG_C_Atom', 'id': '36a8998f', '_meta': {'source': 'web_app', 'group': 'g_input', 'timestamp': '2025-06-30T06:24:30.099Z', 'accessToken': 'provided'}}
2025-06-30 13:25:35,716 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:SIG_INSTANT :: {'a': 'Buy Now', 'ptp': 2000, 'psl': 1000, 'lot': 0.01, 's': 'XAUUSD', 'c': 'SIG_INSTANT', '_meta': {'source': 'web_app', 'group': 'g_instant', 'timestamp': '2025-06-30T06:25:34.893Z', 'accessToken': 'provided'}}
2025-06-30 13:30:07,779 [INFO] \u2705 Webhook Instant: s:XAUUSD a:Buy Now c:WH_ER2_RSMA_E1 :: {'a': 'Buy Now', 'c': 'WH_ER2_RSMA_E1', 'p': 3290.64, 'stp': 9, 'xtp': 3, 'tp': 3317.64, 'sl': 3281.64, 's': 'XAUUSD'}
2025-06-30 13:30:08,482 [WARNING] Task queue depth is 1
2025-06-30 13:31:33,587 [INFO] \U0001f7e2 Orders auto-refresh started
2025-06-30 13:32:29,931 [INFO] \U0001f7e2 Orders auto-refresh started
2025-06-30 13:32:29,939 [INFO] Serving on http://0.0.0.0:5000
2025-06-30 13:32:35,140 [INFO] \U0001f7e2 Orders auto-refresh started
2025-06-30 13:32:43,328 [INFO] \U0001f534 Webhook DISABLED
2025-06-30 13:32:43,647 [INFO] \U0001f7e2 Webhook ENABLED
2025-06-30 13:33:18,815 [INFO] \u2705 Webhook Input: s:XAUUSD a:Buy Limit p:3273 c:SIG_C_Atom :: {'rows': [{'tp': 3282, 'lot': 0.65}, {'tp': 3289.5, 'lot': 0.2}], 'a': 'Buy Limit', 'p': 3273, 'sl': 3268, 's': 'XAUUSD', 'c': 'SIG_C_Atom', 'id': '36a8998f', '_meta': {'source': 'web_app', 'group': 'g_input', 'timestamp': '2025-06-30T06:33:17.969Z', 'accessToken': 'provided'}}
2025-06-30 13:33:18,890 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-06-30 13:33:19,015 [INFO] \u274c Execute failed: AutoTrading disabled by client
2025-06-30 13:33:19,169 [INFO] \u2705 Webhook Input orders sent with ID: 36a8998f
2025-06-30 14:00:07,398 [INFO] \U0001f534 Orders auto-refresh stopped
