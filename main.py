from App.config import Config
from App.util import Util
from App.tab_account import TabAccount
from App.tab_input import TabInput
from App.tab_instant import TabInstant
# from App.tab_auto import TabAuto
from App.tab_webhook import TabWebhook
from App.tab_control import TabControl
from App.tab_orders import TabOrders
import customtkinter as ctk
import MetaTrader5 as mt5
import threading
# from PIL import Image, ImageDraw
import sys
# import objgraph

# ===========================
# Main App Setup
# ===========================
class MyApp:
    def __init__(self):
        # objgraph.show_growth()  # ดู object ที่เพิ่มขึ้น
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        self.root = ctk.CTk()
        self.root.geometry("550x720+0+0")
        # self.root.attributes("-topmost", True)
        self.config = Config()
        self.root.title(self.config.app_name)

        self.util = Util(self.config)
        self.tabview = ctk.CTkTabview(self.root)
        self.tabview.pack(expand=True, fill="both", padx=20, pady=5)

        # Add a status label at the bottom to show messages
        self.config.status_label_frame = ctk.CTkFrame(self.root)
        self.config.status_label_frame.pack(fill="both", padx=20, pady=5)
        # self.config.timer_label = ctk.CTkLabel(self.config.status_label_frame, text="(0)", text_color="yellow", font=("Arial", 12))
        # self.config.timer_label.pack(side="right", padx=10, pady=10)
        self.config.status_label = ctk.CTkLabel(self.config.status_label_frame, text="Status: Ready", text_color="yellow", font=("Arial", 12))
        self.config.status_label.pack(side="right", padx=10, pady=5)

        self.config.status_scroll_frame = ctk.CTkScrollableFrame(self.root, width=480, height=250)
        self.config.status_scroll_frame.pack(padx=20, pady=5, fill="both", expand=False)
        
        self.account_tab = TabAccount(self.tabview, self.config, self.util)
        self.input_tab = TabInput(self.tabview, self.config, self.util)
        self.instant_tab = TabInstant(self.tabview, self.config, self.util)
        # self.auto_tab = TabAuto(self.tabview, self.config, self.util)
        self.webhook_tab = TabWebhook(self.tabview, self.config, self.util)
        self.control_tab = TabControl(self.tabview, self.config, self.util)
        self.orders_tab = TabOrders(self.tabview, self.config, self.util)

        # --- รัน Flask ใน background thread ---
        flask_thread = threading.Thread(target=self.webhook_tab.run_webhook, daemon=True)
        flask_thread.start()

        # objgraph.show_growth()  # ดู object ที่เพิ่มขึ้น
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        self.root.mainloop()

    def on_close(self):
        mt5.shutdown()
        self.root.destroy()

    def minimize_to_tray(self):
        self.root.withdraw()  # ซ่อนหน้าต่าง

        def on_restore(icon, item):
            icon.stop()
            self.root.deiconify()

        def on_exit(icon, item):
            icon.stop()
            self.root.destroy()
            sys.exit()

        # menu = pystray.Menu(
        #     pystray.MenuItem("Restore", on_restore),
        #     pystray.MenuItem("Exit", on_exit)
        # )
        # icon = pystray.Icon(self.config.app_key, Image.open("icon.png"), self.config.app_name, menu)

        # threading.Thread(target=icon.run, daemon=True).start()

if __name__ == "__main__":
    MyApp()
