#!/usr/bin/env python3
"""
Test script to validate webhook server fixes
This script tests the webhook server functionality without freezing issues
"""

import requests
import json
import time
import threading
import os

# Test configuration
WEBHOOK_URL = "http://localhost:5000"
ACCESS_TOKEN = os.getenv('WEBHOOK_ACCESS_TOKEN', 'ec2a9c0db08fdda7ca38f346ebf34eb0ab3a8ff918db3a9d9fed2a71f68865a8')

def test_webhook_connection():
    """Test basic webhook server connection"""
    try:
        response = requests.get(f"{WEBHOOK_URL}/webhook_orders_data", 
                              headers={
                                  'Authorization': f'Bearer {ACCESS_TOKEN}',
                                  'X-Access-Token': ACCESS_TOKEN
                              },
                              timeout=5)
        print(f"✅ Webhook server connection: {response.status_code}")
        return response.status_code == 200
    except requests.exceptions.RequestException as e:
        print(f"❌ Webhook server connection failed: {e}")
        return False

def test_webhook_input():
    """Test webhook input endpoint"""
    test_data = {
        "s": "EURUSD",
        "a": "Buy Now",
        "p": 1.1000,
        "c": "TEST",
        "sl": 1.0950,
        "id": "test123",
        "rows": [
            {"tp": 1.1050, "lot": 0.01},
            {"tp": 1.1100, "lot": 0.01}
        ]
    }
    
    try:
        response = requests.post(f"{WEBHOOK_URL}/webhook_input",
                               json=test_data,
                               headers={
                                   'Authorization': f'Bearer {ACCESS_TOKEN}',
                                   'X-Access-Token': ACCESS_TOKEN,
                                   'Content-Type': 'application/json'
                               },
                               timeout=10)
        print(f"✅ Webhook input test: {response.status_code} - {response.text}")
        return response.status_code == 200
    except requests.exceptions.RequestException as e:
        print(f"❌ Webhook input test failed: {e}")
        return False

def stress_test_webhook(duration=30, interval=2):
    """Stress test webhook server for specified duration"""
    print(f"🔄 Starting stress test for {duration} seconds...")
    start_time = time.time()
    success_count = 0
    error_count = 0
    
    while time.time() - start_time < duration:
        try:
            if test_webhook_connection():
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            print(f"❌ Stress test error: {e}")
            error_count += 1
        
        time.sleep(interval)
    
    print(f"📊 Stress test results: {success_count} success, {error_count} errors")
    return error_count == 0

def test_concurrent_requests(num_threads=5, requests_per_thread=10):
    """Test concurrent webhook requests"""
    print(f"🔄 Testing {num_threads} concurrent threads with {requests_per_thread} requests each...")
    
    results = []
    
    def worker():
        thread_results = []
        for i in range(requests_per_thread):
            success = test_webhook_connection()
            thread_results.append(success)
            time.sleep(0.1)  # Small delay between requests
        results.extend(thread_results)
    
    # Start threads
    threads = []
    for i in range(num_threads):
        thread = threading.Thread(target=worker)
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    success_count = sum(results)
    total_requests = len(results)
    print(f"📊 Concurrent test results: {success_count}/{total_requests} successful")
    
    return success_count == total_requests

def main():
    """Run all webhook tests"""
    print("🚀 Starting webhook server validation tests...")
    print("=" * 50)
    
    # Wait for server to start
    print("⏳ Waiting for webhook server to start...")
    time.sleep(3)
    
    # Test 1: Basic connection
    print("\n1. Testing basic webhook connection...")
    if not test_webhook_connection():
        print("❌ Basic connection test failed. Make sure the application is running.")
        return False
    
    # Test 2: Webhook input functionality
    print("\n2. Testing webhook input endpoint...")
    test_webhook_input()  # This might fail if MT5 is not connected, but shouldn't crash
    
    # Test 3: Stress test
    print("\n3. Running stress test (30 seconds)...")
    stress_test_result = stress_test_webhook(duration=30, interval=1)
    
    # Test 4: Concurrent requests
    print("\n4. Testing concurrent requests...")
    concurrent_result = test_concurrent_requests(num_threads=3, requests_per_thread=5)
    
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Basic Connection: ✅")
    print(f"   Stress Test: {'✅' if stress_test_result else '❌'}")
    print(f"   Concurrent Test: {'✅' if concurrent_result else '❌'}")
    
    if stress_test_result and concurrent_result:
        print("\n🎉 All tests passed! Webhook server appears stable.")
        return True
    else:
        print("\n⚠️  Some tests failed. Check the application for issues.")
        return False

if __name__ == "__main__":
    main()
