
import MetaTrader5 as mt5
import customtkinter as ctk
import psutil
import time
import subprocess
import os
from datetime import datetime
from flask import Flask, request, jsonify
from waitress import serve

# ===========================
# Class: TabAccount
# ===========================
class TabAccount:
    def __init__(self, master, config, util):
        self.frame = master.add("Account")
        self.config = config
        self.util = util 
        self.config.symbol_posfix = ctk.StringVar()
        self.account_var = ctk.StringVar(value=self.config.account_default)
        ctk.CTkLabel(self.frame, text="** Add Symbol to watchlist and enable Algo before use this app").pack(pady=5)
        ctk.CTkLabel(self.frame, text="Select account:").pack(pady=5)
        ctk.CTkOptionMenu(self.frame, values=list(self.config.accounts), variable=self.account_var).pack()
        ctk.CTkButton(self.frame, text="Connect", command=self.connect_to_mt5).pack(pady=10)

        self.account_status_label = ctk.CTkLabel(self.config.status_label_frame, text="Connection: None", text_color="yellow", font=("Arial", 12))
        self.account_status_label.pack(side="left", padx=10, pady=10)

        self.connect_to_mt5()
    
    def connect_to_mt5(self):
        prefix = self.config.accounts[self.account_var.get()]
        login = int(os.getenv(f"{prefix}_LOGIN"))
        pwd = os.getenv(f"{prefix}_PWD")
        server = os.getenv(f"{prefix}_SERVER")
        timezone = os.getenv(f"{prefix}_TZ")
        path = os.getenv("MT5_PATH" , "C:\\Program Files\\MetaTrader 5\\terminal64.exe")
        self.config.symbol_posfix.set(os.getenv(f'{prefix}_SYMBOL_POSTFIX')) 
        # self.start_mt5(path)
        # if not mt5.initialize():
        if not mt5.initialize(path=path):
            self.account_status_label.configure(text="MT5 initialization failed", text_color="red")
            return
        
        if mt5.login(login, pwd, server):
            self.account_status_label.configure(text=f"✅ Logged in: {server} (TZ-{timezone})", text_color="lime")
        else:
            self.account_status_label.configure(text=f"❌ Login failed: {prefix}_LOGIN {mt5.last_error()}", text_color="red")
        
    def is_mt5_running(self):
        """Check if MetaTrader 5 is already running"""
        for process in psutil.process_iter(attrs=["name"]):
            if "terminal64.exe" in process.info["name"]:
                return True
        return False

    def start_mt5(self, path):
        """ Start MT5 in the background if it's not running """
        if not self.is_mt5_running():
            print("Starting MT5...")
            subprocess.Popen(
                path, 
                # shell=True, 
                creationflags=subprocess.CREATE_NO_WINDOW  # Hide MT5 window
            )
            time.sleep(5)  # Wait for MT5 to initialize