
import MetaTrader5 as mt5
import customtkinter as ctk
import os
import threading
import time
from datetime import datetime
import tkinter as tk
from tkinter import ttk
import re
from collections import defaultdict

# ===========================
# Class: TabOrders
# ===========================
class TabOrders:
    def __init__(self, master, config, util):
        self.frame = master.add("Orders")
        self.config = config
        self.util = util
        self.loop_running = False
        self.thread1 = None
        self.time_var = ctk.IntVar(value=5)  # Default 5 seconds refresh

        # Separate controls for auto refresh and auto BE
        self.auto_refresh_enabled = False  # Auto refresh disabled by default
        self.auto_be_enabled = True  # Auto BE can work independently

        # Auto SL to BE settings for each filter type
        self.auto_be_all_enabled = True  # Enabled by default for "no filter"
        self.auto_be_zd_enabled = False
        self.auto_be_in_enabled = False

        # Circuit breaker for MT5 operations
        self.mt5_error_count = 0
        self.max_mt5_errors = 5
        self.last_successful_operation = time.time()

        # Create main control frame
        self.control_frame = ctk.CTkFrame(self.frame)
        self.control_frame.pack(fill="x", padx=10, pady=5)

        # Create tabview for subtabs
        self.tabview = ctk.CTkTabview(self.frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=5)

        # Add subtabs
        self.all_orders_tab = self.tabview.add("All Orders")
        self.zd_orders_tab = self.tabview.add("ZD Orders")
        self.in_orders_tab = self.tabview.add("IN Orders")

        self.build_control_panel()
        self.build_all_orders_tab()
        self.build_zd_orders_tab()
        self.build_in_orders_tab()

        # Initialize status display
        self.update_status_display()

        # Perform initial data load
        self.refresh_all_data()

        # Start loop if auto BE is enabled (even if auto refresh is disabled)
        if self.has_auto_be_enabled():
            self.start_loop()
            self.util.add_status_frame("📋 TabOrders initialized - Auto BE enabled, auto refresh disabled")
        else:
            self.util.add_status_frame("📋 TabOrders initialized - All auto functions disabled")

    def build_control_panel(self):
        """Build the control panel with refresh settings"""
        # Create a more organized layout with frames using grid for better space management

        # Top row - Refresh controls
        top_frame = ctk.CTkFrame(self.control_frame)
        top_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(top_frame, text="Auto Refresh:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        self.refresh_switch = ctk.CTkSwitch(top_frame, text="Enable", command=self.toggle_refresh)
        self.refresh_switch.pack(side="left", padx=5)

        ctk.CTkLabel(top_frame, text="Interval (sec):").pack(side="left", padx=5)
        self.time_entry = ctk.CTkEntry(top_frame, textvariable=self.time_var, width=60)
        self.time_entry.pack(side="left", padx=5)

        # Manual refresh button
        ctk.CTkButton(top_frame, text="Refresh Now", command=self.refresh_all_data, width=80).pack(side="left", padx=5)

        # Status display
        self.status_label = ctk.CTkLabel(top_frame, text="Ready", font=ctk.CTkFont(size=12))
        self.status_label.pack(side="right", padx=5)

        # Debug button
        debug_button = ctk.CTkButton(top_frame, text="Debug", width=60, command=self.debug_status)
        debug_button.pack(side="right", padx=5)

        # Bottom row - Auto SL to BE controls
        bottom_frame = ctk.CTkFrame(self.control_frame)
        bottom_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(bottom_frame, text="Auto SL to BE:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        self.auto_be_all_switch = ctk.CTkSwitch(bottom_frame, text="All", command=self.toggle_auto_be_all)
        self.auto_be_all_switch.pack(side="left", padx=5)
        self.auto_be_all_switch.select()  # Enabled by default for "no filter"

        self.auto_be_zd_switch = ctk.CTkSwitch(bottom_frame, text="ZD", command=self.toggle_auto_be_zd)
        self.auto_be_zd_switch.pack(side="left", padx=5)

        self.auto_be_in_switch = ctk.CTkSwitch(bottom_frame, text="IN", command=self.toggle_auto_be_in)
        self.auto_be_in_switch.pack(side="left", padx=5)

        # Add a visual indicator for enabled auto BE
        self.auto_be_status_label = ctk.CTkLabel(bottom_frame, text="", font=ctk.CTkFont(size=10))
        self.auto_be_status_label.pack(side="right", padx=5)

    def build_all_orders_tab(self):
        """Build the All Orders tab with combined view"""
        # Create frames for positions and pending orders
        pos_frame = ctk.CTkFrame(self.all_orders_tab)
        pos_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pos_frame, text="Open Positions", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Positions treeview
        self.positions_tree = ttk.Treeview(pos_frame, columns=("Symbol", "Type", "Volume", "Entry", "Current", "SL", "TP", "Profit", "Comment"), show="headings", height=8)

        # Configure columns
        columns = [("Symbol", 80), ("Type", 60), ("Volume", 70), ("Entry", 80), ("Current", 80), ("SL", 80), ("TP", 80), ("Profit", 80), ("Comment", 150)]
        for col, width in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=width, anchor="center")

        # Add scrollbar for positions
        pos_scrollbar = ttk.Scrollbar(pos_frame, orient="vertical", command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=pos_scrollbar.set)

        self.positions_tree.pack(side="left", fill="both", expand=True)
        pos_scrollbar.pack(side="right", fill="y")

        # Pending orders frame
        pending_frame = ctk.CTkFrame(self.all_orders_tab)
        pending_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pending_frame, text="Pending Orders", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Pending orders treeview
        self.pending_tree = ttk.Treeview(pending_frame, columns=("Symbol", "Type", "Volume", "Entry", "SL", "TP", "Comment"), show="headings", height=8)

        # Configure columns for pending orders
        pending_columns = [("Symbol", 80), ("Type", 80), ("Volume", 70), ("Entry", 80), ("SL", 80), ("TP", 80), ("Comment", 150)]
        for col, width in pending_columns:
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=width, anchor="center")

        # Add scrollbar for pending orders
        pending_scrollbar = ttk.Scrollbar(pending_frame, orient="vertical", command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        self.pending_tree.pack(side="left", fill="both", expand=True)
        pending_scrollbar.pack(side="right", fill="y")

    def build_zd_orders_tab(self):
        """Build the ZD Orders tab with grouped view"""
        # ZD groups list frame
        zd_list_frame = ctk.CTkFrame(self.zd_orders_tab)
        zd_list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(zd_list_frame, text="ZD Groups", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # ZD groups treeview
        self.zd_groups_tree = ttk.Treeview(zd_list_frame, columns=("ID", "TP_Count", "Total_Volume", "Total_Profit", "Actions"), show="headings", height=15)

        # Configure columns for ZD groups
        zd_columns = [("ID", 120), ("TP_Count", 80), ("Total_Volume", 100), ("Total_Profit", 100), ("Actions", 100)]
        for col, width in zd_columns:
            self.zd_groups_tree.heading(col, text=col)
            self.zd_groups_tree.column(col, width=width, anchor="center")

        # Add scrollbar for ZD groups
        zd_scrollbar = ttk.Scrollbar(zd_list_frame, orient="vertical", command=self.zd_groups_tree.yview)
        self.zd_groups_tree.configure(yscrollcommand=zd_scrollbar.set)

        self.zd_groups_tree.pack(side="left", fill="both", expand=True)
        zd_scrollbar.pack(side="right", fill="y")

        # Bind double-click event to close ZD group
        self.zd_groups_tree.bind("<Double-1>", self.on_zd_group_double_click)

        # Close button for selected ZD group
        zd_button_frame = ctk.CTkFrame(self.zd_orders_tab)
        zd_button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(zd_button_frame, text="Close Selected ZD Group",
                     fg_color="red", hover_color="darkred",
                     command=self.close_selected_zd_group).pack(side="right", padx=5)

    def build_in_orders_tab(self):
        """Build the INPUT Orders tab with grouped view"""
        # INPUT groups list frame
        in_list_frame = ctk.CTkFrame(self.in_orders_tab)
        in_list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(in_list_frame, text="INPUT Groups", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # INPUT groups treeview
        self.in_groups_tree = ttk.Treeview(in_list_frame, columns=("INPUT_ID", "TP_Count", "Total_Volume", "Total_Profit", "Actions"), show="headings", height=15)

        # Configure columns for INPUT groups
        in_columns = [("INPUT_ID", 120), ("TP_Count", 80), ("Total_Volume", 100), ("Total_Profit", 100), ("Actions", 100)]
        for col, width in in_columns:
            self.in_groups_tree.heading(col, text=col)
            self.in_groups_tree.column(col, width=width, anchor="center")

        # Add scrollbar for INPUT groups
        in_scrollbar = ttk.Scrollbar(in_list_frame, orient="vertical", command=self.in_groups_tree.yview)
        self.in_groups_tree.configure(yscrollcommand=in_scrollbar.set)

        self.in_groups_tree.pack(side="left", fill="both", expand=True)
        in_scrollbar.pack(side="right", fill="y")

        # Bind double-click event to close INPUT group
        self.in_groups_tree.bind("<Double-1>", self.on_in_group_double_click)

        # Close button for selected INPUT group
        in_button_frame = ctk.CTkFrame(self.in_orders_tab)
        in_button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(in_button_frame, text="Close Selected INPUT Group",
                     fg_color="red", hover_color="darkred",
                     command=self.close_selected_in_group).pack(side="right", padx=5)

    # Auto SL to BE toggle methods
    def toggle_auto_be_all(self):
        """Toggle auto SL to BE for all orders (no filter)"""
        self.auto_be_all_enabled = self.auto_be_all_switch.get()
        status = "enabled" if self.auto_be_all_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (All orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def toggle_auto_be_zd(self):
        """Toggle auto SL to BE for ZD orders"""
        self.auto_be_zd_enabled = self.auto_be_zd_switch.get()
        status = "enabled" if self.auto_be_zd_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (ZD orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def toggle_auto_be_in(self):
        """Toggle auto SL to BE for INPUT orders"""
        self.auto_be_in_enabled = self.auto_be_in_switch.get()
        status = "enabled" if self.auto_be_in_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (INPUT orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def _manage_loop_for_auto_be(self):
        """Start or stop loop based on auto BE settings"""
        if self.has_auto_be_enabled():
            # Start loop if any auto BE is enabled
            if not self.loop_running:
                self.start_loop()
        else:
            # Stop loop only if auto refresh is also disabled
            if not self.auto_refresh_enabled:
                self.stop_loop()

    def update_status_display(self):
        """Update the status display with current settings"""
        # Show separate status for refresh and auto BE
        refresh_status = "ON" if self.auto_refresh_enabled else "OFF"
        loop_status = "RUNNING" if self.loop_running else "STOPPED"

        be_statuses = []
        if self.auto_be_all_enabled:
            be_statuses.append("All")
        if self.auto_be_zd_enabled:
            be_statuses.append("ZD")
        if self.auto_be_in_enabled:
            be_statuses.append("IN")

        be_status = "+".join(be_statuses) if be_statuses else "OFF"
        status_text = f"Refresh: {refresh_status} | Loop: {loop_status} | Auto BE: {be_status}"

        if hasattr(self, 'status_label'):
            self.status_label.configure(text=status_text)

        # Update auto BE status label with color coding
        if hasattr(self, 'auto_be_status_label'):
            if be_statuses:
                self.auto_be_status_label.configure(text=f"✅ Active: {be_status}", text_color="lime")
            else:
                self.auto_be_status_label.configure(text="❌ Inactive", text_color="red")

    def debug_status(self):
        """Debug method to check current status"""
        debug_info = [
            f"Loop Running: {self.loop_running}",
            f"Auto BE All: {self.auto_be_all_enabled}",
            f"Auto BE ZD: {self.auto_be_zd_enabled}",
            f"Auto BE IN: {self.auto_be_in_enabled}",
            f"Refresh Switch: {self.refresh_switch.get() if hasattr(self, 'refresh_switch') else 'N/A'}",
            f"Time Interval: {self.time_var.get()}",
        ]

        debug_message = "🔍 DEBUG STATUS:\n" + "\n".join(debug_info)
        self.util.add_status_frame(debug_message)
        print(debug_message)  # Also print to console

    # Data refresh and management methods
    def toggle_refresh(self):
        """Toggle auto refresh on/off"""
        self.auto_refresh_enabled = self.refresh_switch.get()

        # Start or manage the loop based on what's enabled
        if self.auto_refresh_enabled or self.has_auto_be_enabled():
            self.start_loop()
        else:
            self.stop_loop()

        status = "enabled" if self.auto_refresh_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto refresh {status}")
        self.update_status_display()

    def has_auto_be_enabled(self):
        """Check if any auto BE mode is enabled"""
        return (self.auto_be_all_enabled or
                self.auto_be_zd_enabled or
                self.auto_be_in_enabled)

    def check_mt5_health(self):
        """Check MT5 connection health and implement circuit breaker"""
        try:
            # Quick connection test
            if not mt5.initialize():
                self.mt5_error_count += 1
                return False

            # Reset error count on successful connection
            self.mt5_error_count = 0
            self.last_successful_operation = time.time()
            return True

        except Exception as e:
            self.mt5_error_count += 1
            self.util.add_status_frame(f"❌ MT5 health check failed: {e}", "red")
            return False

    def should_skip_mt5_operations(self):
        """Determine if MT5 operations should be skipped due to errors"""
        if self.mt5_error_count >= self.max_mt5_errors:
            time_since_last_success = time.time() - self.last_successful_operation
            if time_since_last_success < 300:  # 5 minutes cooldown
                return True
            else:
                # Reset after cooldown period
                self.mt5_error_count = 0
        return False

    def start_loop(self):
        """Start the refresh loop"""
        if not self.loop_running:
            self.loop_running = True
            self.thread1 = threading.Thread(target=self.refresh_loop, daemon=True)
            self.thread1.start()
        self.util.add_status_frame("🟢 Orders auto-refresh started")
        self.update_status_display()

    def stop_loop(self):
        """Stop the loop only if both auto refresh and auto BE are disabled"""
        if not self.auto_refresh_enabled and not self.has_auto_be_enabled():
            self.loop_running = False
            self.util.add_status_frame("🔴 All auto functions stopped")
        else:
            # Keep loop running if any auto function is enabled
            reasons = []
            if self.auto_refresh_enabled:
                reasons.append("auto refresh")
            if self.has_auto_be_enabled():
                reasons.append("auto BE")
            self.util.add_status_frame(f"🟡 Loop continues for: {', '.join(reasons)}")

        self.update_status_display()

    def refresh_loop(self):
        """Main refresh loop with improved error handling and connection management"""
        consecutive_errors = 0
        max_consecutive_errors = 5
        loop_count = 0

        try:
            while self.loop_running:
                try:
                    # Check if MT5 is still connected before proceeding
                    if not mt5.initialize():
                        self.util.add_status_frame("⚠️ MT5 connection lost, attempting to reconnect...", "yellow")
                        time.sleep(10)  # Wait before retry
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            self.util.add_status_frame(f"❌ Too many connection errors ({consecutive_errors}), stopping refresh loop", "red")
                            self.loop_running = False
                            break
                        continue

                    # Reset error counter on successful connection
                    consecutive_errors = 0

                    # Perform operations based on what's enabled
                    if self.auto_refresh_enabled:
                        self.refresh_all_data()

                    # Always process auto BE if any mode is enabled
                    if self.has_auto_be_enabled():
                        self.process_auto_be()

                    # Periodic memory cleanup (every 50 loops)
                    loop_count += 1
                    if loop_count % 50 == 0:
                        self.util.cleanup_memory()
                        loop_count = 0  # Reset counter

                    # Adjust sleep time based on what's enabled
                    if self.auto_refresh_enabled:
                        # Use user-defined interval for refresh
                        sleep_time = max(self.time_var.get(), 10)  # Minimum 10 seconds
                    else:
                        # Only auto BE running - use longer interval to save resources
                        sleep_time = max(self.time_var.get() * 2, 30)  # Minimum 30 seconds for BE only

                    time.sleep(sleep_time)

                except Exception as e:
                    consecutive_errors += 1
                    self.util.add_status_frame(f"❌ Refresh loop error ({consecutive_errors}/{max_consecutive_errors}): {e}", "red")

                    if consecutive_errors >= max_consecutive_errors:
                        self.util.add_status_frame(f"❌ Too many consecutive errors, stopping refresh loop", "red")
                        self.loop_running = False
                        break

                    # Wait longer after errors
                    time.sleep(30)

        except Exception as e:
            self.util.add_status_frame(f"❌ Critical refresh loop error: {e}", "red")
        finally:
            self.util.add_status_frame("🔴 Refresh loop stopped", "yellow")

    def process_auto_be(self):
        """Process auto SL to BE for enabled filters with circuit breaker"""
        try:
            # Skip if no auto BE is enabled
            if not self.has_auto_be_enabled():
                return

            # Check if we should skip MT5 operations due to errors
            if self.should_skip_mt5_operations():
                return

            # Check MT5 health
            if not self.check_mt5_health():
                return

            # Get all symbols for processing safely
            positions = self.util.safe_mt5_call(mt5.positions_get)
            if not positions:
                return

            symbols = set(pos.symbol for pos in positions)
            if not symbols:
                return

            # Process each symbol with error handling
            for symbol in symbols:
                try:
                    is_moving_tp = True

                    # Process auto SL to BE for different filters
                    if self.auto_be_all_enabled:
                        self.util.update_SL_to_BE_by_point(symbol, "", is_moving_tp)

                    if self.auto_be_zd_enabled:
                        self.util.update_SL_to_BE_by_point(symbol, "ZD", is_moving_tp)

                    if self.auto_be_in_enabled:
                        self.util.update_SL_to_BE_by_point(symbol, "IN", is_moving_tp)

                except Exception as e:
                    self.util.add_status_frame(f"❌ Auto BE error for {symbol}: {e}", "yellow")
                    continue

        except Exception as e:
            self.mt5_error_count += 1
            self.util.add_status_frame(f"❌ Auto SL to BE error: {e}", "red")

    def refresh_all_data(self):
        """Refresh all order data with improved error handling"""
        try:
            # Check MT5 connection first
            if not mt5.initialize():
                self.util.add_status_frame("❌ MT5 not connected - cannot refresh data", "red")
                return

            # Refresh data with individual error handling
            try:
                self.refresh_positions()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ Position refresh error: {e}", "yellow")

            try:
                self.refresh_pending_orders()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ Pending orders refresh error: {e}", "yellow")

            try:
                self.refresh_zd_groups()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ ZD groups refresh error: {e}", "yellow")

            try:
                self.refresh_in_groups()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ IN groups refresh error: {e}", "yellow")

            # Update status with last refresh time (thread-safe)
            current_time = time.strftime('%H:%M:%S')
            if hasattr(self, 'status_label'):
                def update_status():
                    try:
                        current_text = self.status_label.cget("text")
                        base_text = current_text.split(" | Last:")[0] if " | Last:" in current_text else current_text
                        self.status_label.configure(text=f"{base_text} | Last: {current_time}")
                    except:
                        pass

                # Schedule status update on main thread
                try:
                    self.status_label.after(0, update_status)
                except:
                    update_status()

        except Exception as e:
            self.util.add_status_frame(f"❌ Data refresh error: {e}", "red")

    def refresh_positions(self):
        """Refresh open positions data"""
        # Clear existing data
        for item in self.positions_tree.get_children():
            self.positions_tree.delete(item)

        # Get positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                self.positions_tree.insert("", "end", values=(
                    pos.symbol,
                    "BUY" if pos.type == 0 else "SELL",
                    f"{pos.volume:.2f}",
                    f"{pos.price_open:.5f}",
                    f"{pos.price_current:.5f}",
                    f"{pos.sl:.5f}" if pos.sl > 0 else "None",
                    f"{pos.tp:.5f}" if pos.tp > 0 else "None",
                    f"{pos.profit:.2f}",
                    pos.comment
                ))

    def refresh_pending_orders(self):
        """Refresh pending orders data"""
        # Clear existing data
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        # Get pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                 "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                 "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                 "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                 f"Type {order.type}"

                self.pending_tree.insert("", "end", values=(
                    order.symbol,
                    order_type_name,
                    f"{order.volume_initial:.2f}",
                    f"{order.price_open:.5f}",
                    f"{order.sl:.5f}" if order.sl > 0 else "None",
                    f"{order.tp:.5f}" if order.tp > 0 else "None",
                    order.comment
                ))

    def extract_zd_id(self, comment):
        """Extract ID from comment like 'ZD_XXX_X_X_<ID>'"""
        if not comment.startswith('ZD'):
            return None

        # Split by underscore and get the last part as ID
        parts = comment.split('_')
        if len(parts[-1]) >= 5:  # ZD_XXX_X_X_<ID>
            return parts[-1]  # Return the last part as ID
        return None

    def extract_in_id(self, comment):
        """Extract ID from comment like 'IN_MANUAL_<ID>'"""
        if not comment.startswith('IN'):
            return None
 
        # parts = comment.split('_')
        # if len(parts) >= 3:  # INPUT_MANUAL_<ID>_TP1
        #     # Find the ID (should be 8 characters of lowercase+digits)
        #     for i, part in enumerate(parts):
        #         if len(part) == 8 and part.islower() and any(c.isdigit() for c in part):
        #             return part

        parts = comment.split('_')
        if len(parts[-1]) >= 5:  # INPUT_XXX_X_X_<ID>
            return parts[-1]  # Return the last part as ID
        return None

    def refresh_zd_groups(self):
        """Refresh ZD groups data"""
        # Clear existing data
        for item in self.zd_groups_tree.get_children():
            self.zd_groups_tree.delete(item)

        # Group ZD orders
        zd_groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                zd_id = self.extract_zd_id(pos.comment)
                if zd_id:
                    zd_groups[zd_id]['positions'].append(pos)
                    zd_groups[zd_id]['total_volume'] += pos.volume
                    zd_groups[zd_id]['total_profit'] += pos.profit
                    # if '_TP' in pos.comment:
                    zd_groups[zd_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                zd_id = self.extract_zd_id(order.comment)
                if zd_id:
                    zd_groups[zd_id]['pending'].append(order)
                    zd_groups[zd_id]['total_volume'] += order.volume_initial
                    # if '_TP' in order.comment:
                    zd_groups[zd_id]['tp_count'] += 1

        # Populate treeview
        for zd_id, data in zd_groups.items():
            self.zd_groups_tree.insert("", "end", values=(
                zd_id,
                data['tp_count'],
                f"{data['total_volume']:.2f}",
                f"{data['total_profit']:.2f}",
                f"P:{len(data['positions'])} O:{len(data['pending'])}"
            ))

    def refresh_in_groups(self):
        """Refresh INPUT groups data"""
        # Clear existing data
        for item in self.in_groups_tree.get_children():
            self.in_groups_tree.delete(item)

        # Group INPUT orders
        in_groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                in_id = self.extract_in_id(pos.comment)
                # print(f"INPUT_ID: {in_id} from comment: {pos.comment}")
                if in_id:
                    in_groups[in_id]['positions'].append(pos)
                    in_groups[in_id]['total_volume'] += pos.volume
                    in_groups[in_id]['total_profit'] += pos.profit
                    # if '_TP' in pos.comment:
                    in_groups[in_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        # print(orders)
        if orders:
            for order in orders:
                in_id = self.extract_in_id(order.comment)
                # print(f"INPUT_ID: {in_id} from comment: {order.comment}")
                if in_id:
                    in_groups[in_id]['pending'].append(order)
                    in_groups[in_id]['total_volume'] += order.volume_initial
                    # if '_TP' in order.comment:
                    in_groups[in_id]['tp_count'] += 1

        # Populate treeview
        for in_id, data in in_groups.items():
            self.in_groups_tree.insert("", "end", values=(
                in_id,
                data['tp_count'],
                f"{data['total_volume']:.2f}",
                f"{data['total_profit']:.2f}",
                f"P:{len(data['positions'])} O:{len(data['pending'])}"
            ))

    # Event handlers for closing groups
    def on_zd_group_double_click(self, event):
        """Handle double-click on ZD group"""
        selection = self.zd_groups_tree.selection()
        if selection:
            item = self.zd_groups_tree.item(selection[0])
            zd_id = item['values'][0]
            self.close_zd_group(zd_id)

    def on_in_group_double_click(self, event):
        """Handle double-click on IN group"""
        selection = self.in_groups_tree.selection()
        if selection:
            item = self.in_groups_tree.item(selection[0])
            in_id = item['values'][0]
            self.close_in_group(in_id)

    def close_selected_zd_group(self):
        """Close selected ZD group from button"""
        selection = self.zd_groups_tree.selection()
        if selection:
            item = self.zd_groups_tree.item(selection[0])
            zd_id = item['values'][0]
            self.close_zd_group(zd_id)
        else:
            self.util.add_status_frame("❌ Please select a ZD group to close", "yellow")

    def close_selected_in_group(self):
        """Close selected IN group from button"""
        selection = self.in_groups_tree.selection()
        if selection:
            item = self.in_groups_tree.item(selection[0])
            in_id = item['values'][0]
            self.close_in_group(in_id)
        else:
            self.util.add_status_frame("❌ Please select an IN group to close", "yellow")

    def close_zd_group(self, zd_id):
        """Close all orders (positions and pending) that end with the given ID"""
        closed_positions = 0
        closed_pending = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if pos.comment.endswith(zd_id):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close ZD group {zd_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if order.comment.endswith(zd_id):
                    # Cancel pending order
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_pending += 1

        self.util.add_status_frame(f"✅ Closed ZD group {zd_id}: {closed_positions} positions, {closed_pending} pending orders", "green")

    def close_in_group(self, in_id):
        """Close all orders (positions and pending) that end with the given INPUT_ID"""
        closed_positions = 0
        closed_pending = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if in_id in pos.comment and pos.comment.startswith('INPUT'):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close IN group {in_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if in_id in order.comment and order.comment.startswith('INPUT'):
                    # Cancel pending order
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_pending += 1

        self.util.add_status_frame(f"✅ Closed IN group {in_id}: {closed_positions} positions, {closed_pending} pending orders", "green")
     