
import MetaTrader5 as mt5
import customtkinter as ctk
import os
import threading
import time
from datetime import datetime
import tkinter as tk
from tkinter import ttk
import re
from collections import defaultdict

# ===========================
# Class: TabOrders
# ===========================
class TabOrders:
    def __init__(self, master, config, util):
        self.frame = master.add("Orders")
        self.config = config
        self.util = util
        self.loop_running = False
        self.thread1 = None
        self.time_var = ctk.IntVar(value=5)  # Default 5 seconds refresh

        # Separate controls for auto refresh and auto BE
        self.auto_refresh_enabled = False  # Auto refresh disabled by default
        self.auto_be_enabled = True  # Auto BE can work independently

        # Auto SL to BE settings for each filter type
        self.auto_be_all_enabled = True  # Enabled by default for "no filter"
        self.auto_be_sig_enabled = False
        self.auto_be_input_enabled = False

        # Create main control frame
        self.control_frame = ctk.CTkFrame(self.frame)
        self.control_frame.pack(fill="x", padx=10, pady=5)

        # Create tabview for subtabs
        self.tabview = ctk.CTkTabview(self.frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=5)

        # Add subtabs
        self.all_orders_tab = self.tabview.add("All Orders")
        self.sig_orders_tab = self.tabview.add("SIG Orders")
        self.input_orders_tab = self.tabview.add("INPUT Orders")

        self.build_control_panel()
        self.build_all_orders_tab()
        self.build_sig_orders_tab()
        self.build_input_orders_tab()

        # Initialize status display
        self.update_status_display()

        # Perform initial data load
        self.refresh_all_data()

        # Start loop if auto BE is enabled (even if auto refresh is disabled)
        if self.has_auto_be_enabled():
            self.start_loop()
            self.util.add_status_frame("📋 TabOrders initialized - Auto BE enabled, auto refresh disabled")
        else:
            self.util.add_status_frame("📋 TabOrders initialized - All auto functions disabled")

    def build_control_panel(self):
        """Build the control panel with refresh settings"""
        # Create a more organized layout with frames using grid for better space management

        # Top row - Refresh controls
        top_frame = ctk.CTkFrame(self.control_frame)
        top_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(top_frame, text="Auto Refresh:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        self.refresh_switch = ctk.CTkSwitch(top_frame, text="Enable", command=self.toggle_refresh)
        self.refresh_switch.pack(side="left", padx=5)

        ctk.CTkLabel(top_frame, text="Interval (sec):").pack(side="left", padx=5)
        self.time_entry = ctk.CTkEntry(top_frame, textvariable=self.time_var, width=60)
        self.time_entry.pack(side="left", padx=5)

        # Manual refresh button
        ctk.CTkButton(top_frame, text="Refresh Now", command=self.refresh_all_data, width=80).pack(side="left", padx=5)

        # Status display
        self.status_label = ctk.CTkLabel(top_frame, text="Ready", font=ctk.CTkFont(size=12))
        self.status_label.pack(side="right", padx=5)

        # Debug button
        debug_button = ctk.CTkButton(top_frame, text="Debug", width=60, command=self.debug_status)
        debug_button.pack(side="right", padx=5)

        # Bottom row - Auto SL to BE controls
        bottom_frame = ctk.CTkFrame(self.control_frame)
        bottom_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(bottom_frame, text="Auto SL to BE:", font=ctk.CTkFont(weight="bold")).pack(side="left", padx=5)

        self.auto_be_all_switch = ctk.CTkSwitch(bottom_frame, text="All", command=self.toggle_auto_be_all)
        self.auto_be_all_switch.pack(side="left", padx=5)
        self.auto_be_all_switch.select()  # Enabled by default for "no filter"

        self.auto_be_sig_switch = ctk.CTkSwitch(bottom_frame, text="SIG", command=self.toggle_auto_be_sig)
        self.auto_be_sig_switch.pack(side="left", padx=5)

        self.auto_be_input_switch = ctk.CTkSwitch(bottom_frame, text="INPUT", command=self.toggle_auto_be_input)
        self.auto_be_input_switch.pack(side="left", padx=5)

        # Add a visual indicator for enabled auto BE
        self.auto_be_status_label = ctk.CTkLabel(bottom_frame, text="", font=ctk.CTkFont(size=10))
        self.auto_be_status_label.pack(side="right", padx=5)

    def build_all_orders_tab(self):
        """Build the All Orders tab with combined view"""
        # Create frames for positions and pending orders
        pos_frame = ctk.CTkFrame(self.all_orders_tab)
        pos_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pos_frame, text="Open Positions", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Positions treeview
        self.positions_tree = ttk.Treeview(pos_frame, columns=("Symbol", "Type", "Volume", "Entry", "Current", "SL", "TP", "Profit", "Comment"), show="headings", height=8)

        # Configure columns
        columns = [("Symbol", 80), ("Type", 60), ("Volume", 70), ("Entry", 80), ("Current", 80), ("SL", 80), ("TP", 80), ("Profit", 80), ("Comment", 150)]
        for col, width in columns:
            self.positions_tree.heading(col, text=col)
            self.positions_tree.column(col, width=width, anchor="center")

        # Add scrollbar for positions
        pos_scrollbar = ttk.Scrollbar(pos_frame, orient="vertical", command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=pos_scrollbar.set)

        self.positions_tree.pack(side="left", fill="both", expand=True)
        pos_scrollbar.pack(side="right", fill="y")

        # Pending orders frame
        pending_frame = ctk.CTkFrame(self.all_orders_tab)
        pending_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(pending_frame, text="Pending Orders", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Pending orders treeview
        self.pending_tree = ttk.Treeview(pending_frame, columns=("Symbol", "Type", "Volume", "Entry", "SL", "TP", "Comment"), show="headings", height=8)

        # Configure columns for pending orders
        pending_columns = [("Symbol", 80), ("Type", 80), ("Volume", 70), ("Entry", 80), ("SL", 80), ("TP", 80), ("Comment", 150)]
        for col, width in pending_columns:
            self.pending_tree.heading(col, text=col)
            self.pending_tree.column(col, width=width, anchor="center")

        # Add scrollbar for pending orders
        pending_scrollbar = ttk.Scrollbar(pending_frame, orient="vertical", command=self.pending_tree.yview)
        self.pending_tree.configure(yscrollcommand=pending_scrollbar.set)

        self.pending_tree.pack(side="left", fill="both", expand=True)
        pending_scrollbar.pack(side="right", fill="y")

    def build_sig_orders_tab(self):
        """Build the SIG Orders tab with grouped view"""
        # SIG groups list frame
        sig_list_frame = ctk.CTkFrame(self.sig_orders_tab)
        sig_list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(sig_list_frame, text="SIG Groups", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # SIG groups treeview
        self.sig_groups_tree = ttk.Treeview(sig_list_frame, columns=("SIG_ID", "TP_Count", "Total_Volume", "Total_Profit", "Actions"), show="headings", height=15)

        # Configure columns for SIG groups
        sig_columns = [("SIG_ID", 120), ("TP_Count", 80), ("Total_Volume", 100), ("Total_Profit", 100), ("Actions", 100)]
        for col, width in sig_columns:
            self.sig_groups_tree.heading(col, text=col)
            self.sig_groups_tree.column(col, width=width, anchor="center")

        # Add scrollbar for SIG groups
        sig_scrollbar = ttk.Scrollbar(sig_list_frame, orient="vertical", command=self.sig_groups_tree.yview)
        self.sig_groups_tree.configure(yscrollcommand=sig_scrollbar.set)

        self.sig_groups_tree.pack(side="left", fill="both", expand=True)
        sig_scrollbar.pack(side="right", fill="y")

        # Bind double-click event to close SIG group
        self.sig_groups_tree.bind("<Double-1>", self.on_sig_group_double_click)

        # Close button for selected SIG group
        sig_button_frame = ctk.CTkFrame(self.sig_orders_tab)
        sig_button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(sig_button_frame, text="Close Selected SIG Group",
                     fg_color="red", hover_color="darkred",
                     command=self.close_selected_sig_group).pack(side="right", padx=5)

    def build_input_orders_tab(self):
        """Build the INPUT Orders tab with grouped view"""
        # INPUT groups list frame
        input_list_frame = ctk.CTkFrame(self.input_orders_tab)
        input_list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        ctk.CTkLabel(input_list_frame, text="INPUT Groups", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # INPUT groups treeview
        self.input_groups_tree = ttk.Treeview(input_list_frame, columns=("INPUT_ID", "TP_Count", "Total_Volume", "Total_Profit", "Actions"), show="headings", height=15)

        # Configure columns for INPUT groups
        input_columns = [("INPUT_ID", 120), ("TP_Count", 80), ("Total_Volume", 100), ("Total_Profit", 100), ("Actions", 100)]
        for col, width in input_columns:
            self.input_groups_tree.heading(col, text=col)
            self.input_groups_tree.column(col, width=width, anchor="center")

        # Add scrollbar for INPUT groups
        input_scrollbar = ttk.Scrollbar(input_list_frame, orient="vertical", command=self.input_groups_tree.yview)
        self.input_groups_tree.configure(yscrollcommand=input_scrollbar.set)

        self.input_groups_tree.pack(side="left", fill="both", expand=True)
        input_scrollbar.pack(side="right", fill="y")

        # Bind double-click event to close INPUT group
        self.input_groups_tree.bind("<Double-1>", self.on_input_group_double_click)

        # Close button for selected INPUT group
        input_button_frame = ctk.CTkFrame(self.input_orders_tab)
        input_button_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkButton(input_button_frame, text="Close Selected INPUT Group",
                     fg_color="red", hover_color="darkred",
                     command=self.close_selected_input_group).pack(side="right", padx=5)

    # Auto SL to BE toggle methods
    def toggle_auto_be_all(self):
        """Toggle auto SL to BE for all orders (no filter)"""
        self.auto_be_all_enabled = self.auto_be_all_switch.get()
        status = "enabled" if self.auto_be_all_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (All orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def toggle_auto_be_sig(self):
        """Toggle auto SL to BE for SIG orders"""
        self.auto_be_sig_enabled = self.auto_be_sig_switch.get()
        status = "enabled" if self.auto_be_sig_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (SIG orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def toggle_auto_be_input(self):
        """Toggle auto SL to BE for INPUT orders"""
        self.auto_be_input_enabled = self.auto_be_input_switch.get()
        status = "enabled" if self.auto_be_input_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto SL to BE (INPUT orders) {status}")
        self._manage_loop_for_auto_be()
        self.update_status_display()

    def _manage_loop_for_auto_be(self):
        """Start or stop loop based on auto BE settings"""
        if self.has_auto_be_enabled():
            # Start loop if any auto BE is enabled
            if not self.loop_running:
                self.start_loop()
        else:
            # Stop loop only if auto refresh is also disabled
            if not self.auto_refresh_enabled:
                self.stop_loop()

    def update_status_display(self):
        """Update the status display with current settings"""
        # Show separate status for refresh and auto BE
        refresh_status = "ON" if self.auto_refresh_enabled else "OFF"
        loop_status = "RUNNING" if self.loop_running else "STOPPED"

        be_statuses = []
        if self.auto_be_all_enabled:
            be_statuses.append("All")
        if self.auto_be_sig_enabled:
            be_statuses.append("SIG")
        if self.auto_be_input_enabled:
            be_statuses.append("INPUT")

        be_status = "+".join(be_statuses) if be_statuses else "OFF"
        status_text = f"Refresh: {refresh_status} | Loop: {loop_status} | Auto BE: {be_status}"

        if hasattr(self, 'status_label'):
            self.status_label.configure(text=status_text)

        # Update auto BE status label with color coding
        if hasattr(self, 'auto_be_status_label'):
            if be_statuses:
                self.auto_be_status_label.configure(text=f"✅ Active: {be_status}", text_color="lime")
            else:
                self.auto_be_status_label.configure(text="❌ Inactive", text_color="red")

    def debug_status(self):
        """Debug method to check current status"""
        debug_info = [
            f"Loop Running: {self.loop_running}",
            f"Auto BE All: {self.auto_be_all_enabled}",
            f"Auto BE SIG: {self.auto_be_sig_enabled}",
            f"Auto BE INPUT: {self.auto_be_input_enabled}",
            f"Refresh Switch: {self.refresh_switch.get() if hasattr(self, 'refresh_switch') else 'N/A'}",
            f"Time Interval: {self.time_var.get()}",
        ]

        debug_message = "🔍 DEBUG STATUS:\n" + "\n".join(debug_info)
        self.util.add_status_frame(debug_message)
        print(debug_message)  # Also print to console

    # Data refresh and management methods
    def toggle_refresh(self):
        """Toggle auto refresh on/off"""
        self.auto_refresh_enabled = self.refresh_switch.get()

        # Start or manage the loop based on what's enabled
        if self.auto_refresh_enabled or self.has_auto_be_enabled():
            self.start_loop()
        else:
            self.stop_loop()

        status = "enabled" if self.auto_refresh_enabled else "disabled"
        self.util.add_status_frame(f"🔄 Auto refresh {status}")
        self.update_status_display()

    def has_auto_be_enabled(self):
        """Check if any auto BE mode is enabled"""
        return (self.auto_be_all_enabled or
                self.auto_be_sig_enabled or
                self.auto_be_input_enabled)

    def start_loop(self):
        """Start the refresh loop"""
        if not self.loop_running:
            self.loop_running = True
            self.thread1 = threading.Thread(target=self.refresh_loop, daemon=True)
            self.thread1.start()
        self.util.add_status_frame("🟢 Orders auto-refresh started")
        self.update_status_display()

    def stop_loop(self):
        """Stop the loop only if both auto refresh and auto BE are disabled"""
        if not self.auto_refresh_enabled and not self.has_auto_be_enabled():
            self.loop_running = False
            self.util.add_status_frame("🔴 All auto functions stopped")
        else:
            # Keep loop running if any auto function is enabled
            reasons = []
            if self.auto_refresh_enabled:
                reasons.append("auto refresh")
            if self.has_auto_be_enabled():
                reasons.append("auto BE")
            self.util.add_status_frame(f"🟡 Loop continues for: {', '.join(reasons)}")

        self.update_status_display()

    def refresh_loop(self):
        """Main refresh loop with improved error handling and connection management"""
        consecutive_errors = 0
        max_consecutive_errors = 5
        loop_count = 0

        try:
            while self.loop_running:
                try:
                    # Check if MT5 is still connected before proceeding
                    if not mt5.initialize():
                        self.util.add_status_frame("⚠️ MT5 connection lost, attempting to reconnect...", "yellow")
                        time.sleep(10)  # Wait before retry
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            self.util.add_status_frame(f"❌ Too many connection errors ({consecutive_errors}), stopping refresh loop", "red")
                            self.loop_running = False
                            break
                        continue

                    # Reset error counter on successful connection
                    consecutive_errors = 0

                    # Perform operations based on what's enabled
                    if self.auto_refresh_enabled:
                        self.refresh_all_data()

                    # Always process auto BE if any mode is enabled
                    if self.has_auto_be_enabled():
                        self.process_auto_be()

                    # Periodic memory cleanup (every 50 loops)
                    loop_count += 1
                    if loop_count % 50 == 0:
                        self.util.cleanup_memory()
                        loop_count = 0  # Reset counter

                    # Adjust sleep time based on what's enabled
                    if self.auto_refresh_enabled:
                        # Use user-defined interval for refresh
                        sleep_time = max(self.time_var.get(), 10)  # Minimum 10 seconds
                    else:
                        # Only auto BE running - use longer interval to save resources
                        sleep_time = max(self.time_var.get() * 2, 30)  # Minimum 30 seconds for BE only

                    time.sleep(sleep_time)

                except Exception as e:
                    consecutive_errors += 1
                    self.util.add_status_frame(f"❌ Refresh loop error ({consecutive_errors}/{max_consecutive_errors}): {e}", "red")

                    if consecutive_errors >= max_consecutive_errors:
                        self.util.add_status_frame(f"❌ Too many consecutive errors, stopping refresh loop", "red")
                        self.loop_running = False
                        break

                    # Wait longer after errors
                    time.sleep(30)

        except Exception as e:
            self.util.add_status_frame(f"❌ Critical refresh loop error: {e}", "red")
        finally:
            self.util.add_status_frame("🔴 Refresh loop stopped", "yellow")

    def process_auto_be(self):
        """Process auto SL to BE for enabled filters"""
        try:
            # Skip if no auto BE is enabled
            if not (self.auto_be_all_enabled or self.auto_be_sig_enabled or self.auto_be_input_enabled):
                return

            # Check MT5 connection
            if not mt5.initialize():
                return

            # Get all symbols for processing
            symbols = set()
            positions = mt5.positions_get()
            if positions:
                symbols.update(pos.symbol for pos in positions)

            if not symbols:
                return

            for symbol in symbols:
                # Process auto SL to BE for different filters
                if self.auto_be_all_enabled:
                    result = self.util.update_SL_to_BE_by_point(symbol, "", False)
                    if result and result.get('All_FT', 0) > 0:
                        self.util.add_status_frame(f"🔄 Auto BE (All): Updated {result['All_FT']} orders for {symbol}")

                if self.auto_be_sig_enabled:
                    result = self.util.update_SL_to_BE_by_point(symbol, "SIG", False)
                    if result and result.get('All_FT', 0) > 0:
                        self.util.add_status_frame(f"🔄 Auto BE (SIG): Updated {result['All_FT']} orders for {symbol}")

                if self.auto_be_input_enabled:
                    result = self.util.update_SL_to_BE_by_point(symbol, "INPUT", False)
                    if result and result.get('All_FT', 0) > 0:
                        self.util.add_status_frame(f"🔄 Auto BE (INPUT): Updated {result['All_FT']} orders for {symbol}")

        except Exception as e:
            self.util.add_status_frame(f"❌ Auto SL to BE error: {e}", "red")

    def refresh_all_data(self):
        """Refresh all order data with improved error handling"""
        try:
            # Check MT5 connection first
            if not mt5.initialize():
                self.util.add_status_frame("❌ MT5 not connected - cannot refresh data", "red")
                return

            # Refresh data with individual error handling
            try:
                self.refresh_positions()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ Position refresh error: {e}", "yellow")

            try:
                self.refresh_pending_orders()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ Pending orders refresh error: {e}", "yellow")

            try:
                self.refresh_sig_groups()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ SIG groups refresh error: {e}", "yellow")

            try:
                self.refresh_input_groups()
            except Exception as e:
                self.util.add_status_frame(f"⚠️ INPUT groups refresh error: {e}", "yellow")

            # Update status with last refresh time (thread-safe)
            current_time = time.strftime('%H:%M:%S')
            if hasattr(self, 'status_label'):
                def update_status():
                    try:
                        current_text = self.status_label.cget("text")
                        base_text = current_text.split(" | Last:")[0] if " | Last:" in current_text else current_text
                        self.status_label.configure(text=f"{base_text} | Last: {current_time}")
                    except:
                        pass

                # Schedule status update on main thread
                try:
                    self.status_label.after(0, update_status)
                except:
                    update_status()

        except Exception as e:
            self.util.add_status_frame(f"❌ Data refresh error: {e}", "red")

    def refresh_positions(self):
        """Refresh open positions data"""
        # Clear existing data
        for item in self.positions_tree.get_children():
            self.positions_tree.delete(item)

        # Get positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                self.positions_tree.insert("", "end", values=(
                    pos.symbol,
                    "BUY" if pos.type == 0 else "SELL",
                    f"{pos.volume:.2f}",
                    f"{pos.price_open:.5f}",
                    f"{pos.price_current:.5f}",
                    f"{pos.sl:.5f}" if pos.sl > 0 else "None",
                    f"{pos.tp:.5f}" if pos.tp > 0 else "None",
                    f"{pos.profit:.2f}",
                    pos.comment
                ))

    def refresh_pending_orders(self):
        """Refresh pending orders data"""
        # Clear existing data
        for item in self.pending_tree.get_children():
            self.pending_tree.delete(item)

        # Get pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                order_type_name = "Buy Limit" if order.type == mt5.ORDER_TYPE_BUY_LIMIT else \
                                 "Sell Limit" if order.type == mt5.ORDER_TYPE_SELL_LIMIT else \
                                 "Buy Stop" if order.type == mt5.ORDER_TYPE_BUY_STOP else \
                                 "Sell Stop" if order.type == mt5.ORDER_TYPE_SELL_STOP else \
                                 f"Type {order.type}"

                self.pending_tree.insert("", "end", values=(
                    order.symbol,
                    order_type_name,
                    f"{order.volume_initial:.2f}",
                    f"{order.price_open:.5f}",
                    f"{order.sl:.5f}" if order.sl > 0 else "None",
                    f"{order.tp:.5f}" if order.tp > 0 else "None",
                    order.comment
                ))

    def extract_sig_id(self, comment):
        """Extract SIG_ID from comment like 'SIG_XXX_X_X_<SIG_ID>'"""
        if not comment.startswith('SIG'):
            return None

        # Split by underscore and get the last part as SIG_ID
        parts = comment.split('_')
        if len(parts[-1]) >= 5:  # SIG_XXX_X_X_<SIG_ID>
            return parts[-1]  # Return the last part as SIG_ID
        return None

    def extract_input_id(self, comment):
        """Extract INPUT_ID from comment like 'INPUT_MANUAL_<INPUT_ID>'"""
        if not comment.startswith('INPUT'):
            return None
 
        # parts = comment.split('_')
        # if len(parts) >= 3:  # INPUT_MANUAL_<INPUT_ID>_TP1
        #     # Find the INPUT_ID (should be 8 characters of lowercase+digits)
        #     for i, part in enumerate(parts):
        #         if len(part) == 8 and part.islower() and any(c.isdigit() for c in part):
        #             return part

        parts = comment.split('_')
        if len(parts[-1]) >= 5:  # INPUT_XXX_X_X_<INPUT_ID>
            return parts[-1]  # Return the last part as INPUT_ID
        return None

    def refresh_sig_groups(self):
        """Refresh SIG groups data"""
        # Clear existing data
        for item in self.sig_groups_tree.get_children():
            self.sig_groups_tree.delete(item)

        # Group SIG orders
        sig_groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                sig_id = self.extract_sig_id(pos.comment)
                if sig_id:
                    sig_groups[sig_id]['positions'].append(pos)
                    sig_groups[sig_id]['total_volume'] += pos.volume
                    sig_groups[sig_id]['total_profit'] += pos.profit
                    # if '_TP' in pos.comment:
                    sig_groups[sig_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                sig_id = self.extract_sig_id(order.comment)
                if sig_id:
                    sig_groups[sig_id]['pending'].append(order)
                    sig_groups[sig_id]['total_volume'] += order.volume_initial
                    # if '_TP' in order.comment:
                    sig_groups[sig_id]['tp_count'] += 1

        # Populate treeview
        for sig_id, data in sig_groups.items():
            self.sig_groups_tree.insert("", "end", values=(
                sig_id,
                data['tp_count'],
                f"{data['total_volume']:.2f}",
                f"{data['total_profit']:.2f}",
                f"P:{len(data['positions'])} O:{len(data['pending'])}"
            ))

    def refresh_input_groups(self):
        """Refresh INPUT groups data"""
        # Clear existing data
        for item in self.input_groups_tree.get_children():
            self.input_groups_tree.delete(item)

        # Group INPUT orders
        input_groups = defaultdict(lambda: {'positions': [], 'pending': [], 'tp_count': 0, 'total_volume': 0, 'total_profit': 0})

        # Process positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                input_id = self.extract_input_id(pos.comment)
                # print(f"INPUT_ID: {input_id} from comment: {pos.comment}")
                if input_id:
                    input_groups[input_id]['positions'].append(pos)
                    input_groups[input_id]['total_volume'] += pos.volume
                    input_groups[input_id]['total_profit'] += pos.profit
                    # if '_TP' in pos.comment:
                    input_groups[input_id]['tp_count'] += 1

        # Process pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        # print(orders)
        if orders:
            for order in orders:
                input_id = self.extract_input_id(order.comment)
                # print(f"INPUT_ID: {input_id} from comment: {order.comment}")
                if input_id:
                    input_groups[input_id]['pending'].append(order)
                    input_groups[input_id]['total_volume'] += order.volume_initial
                    # if '_TP' in order.comment:
                    input_groups[input_id]['tp_count'] += 1

        # Populate treeview
        for input_id, data in input_groups.items():
            self.input_groups_tree.insert("", "end", values=(
                input_id,
                data['tp_count'],
                f"{data['total_volume']:.2f}",
                f"{data['total_profit']:.2f}",
                f"P:{len(data['positions'])} O:{len(data['pending'])}"
            ))

    # Event handlers for closing groups
    def on_sig_group_double_click(self, event):
        """Handle double-click on SIG group"""
        selection = self.sig_groups_tree.selection()
        if selection:
            item = self.sig_groups_tree.item(selection[0])
            sig_id = item['values'][0]
            self.close_sig_group(sig_id)

    def on_input_group_double_click(self, event):
        """Handle double-click on INPUT group"""
        selection = self.input_groups_tree.selection()
        if selection:
            item = self.input_groups_tree.item(selection[0])
            input_id = item['values'][0]
            self.close_input_group(input_id)

    def close_selected_sig_group(self):
        """Close selected SIG group from button"""
        selection = self.sig_groups_tree.selection()
        if selection:
            item = self.sig_groups_tree.item(selection[0])
            sig_id = item['values'][0]
            self.close_sig_group(sig_id)
        else:
            self.util.add_status_frame("❌ Please select a SIG group to close", "yellow")

    def close_selected_input_group(self):
        """Close selected INPUT group from button"""
        selection = self.input_groups_tree.selection()
        if selection:
            item = self.input_groups_tree.item(selection[0])
            input_id = item['values'][0]
            self.close_input_group(input_id)
        else:
            self.util.add_status_frame("❌ Please select an INPUT group to close", "yellow")

    def close_sig_group(self, sig_id):
        """Close all orders (positions and pending) that end with the given SIG_ID"""
        closed_positions = 0
        closed_pending = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if pos.comment.endswith(sig_id):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close SIG group {sig_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if order.comment.endswith(sig_id):
                    # Cancel pending order
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_pending += 1

        self.util.add_status_frame(f"✅ Closed SIG group {sig_id}: {closed_positions} positions, {closed_pending} pending orders", "green")

    def close_input_group(self, input_id):
        """Close all orders (positions and pending) that end with the given INPUT_ID"""
        closed_positions = 0
        closed_pending = 0

        # Close positions
        positions = mt5.positions_get() if mt5.initialize() else []
        if positions:
            for pos in positions:
                if input_id in pos.comment and pos.comment.startswith('INPUT'):
                    # Close position
                    price = mt5.symbol_info_tick(pos.symbol).bid if pos.type == 0 else mt5.symbol_info_tick(pos.symbol).ask
                    order_type = mt5.ORDER_TYPE_SELL if pos.type == 0 else mt5.ORDER_TYPE_BUY
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "position": pos.ticket,
                        "symbol": pos.symbol,
                        "volume": pos.volume,
                        "type": order_type,
                        "price": price,
                        "deviation": 10,
                        "magic": 155214,
                        "comment": f"Close INPUT group {input_id}",
                        "type_filling": mt5.ORDER_FILLING_FOK,
                    }
                    result = mt5.order_send(close_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_positions += 1

        # Close pending orders
        orders = mt5.orders_get() if mt5.initialize() else []
        if orders:
            for order in orders:
                if input_id in order.comment and order.comment.startswith('INPUT'):
                    # Cancel pending order
                    cancel_request = {
                        "action": mt5.TRADE_ACTION_REMOVE,
                        "order": order.ticket,
                    }
                    result = mt5.order_send(cancel_request)
                    if result.retcode == mt5.TRADE_RETCODE_DONE:
                        closed_pending += 1

        self.util.add_status_frame(f"✅ Closed INPUT group {input_id}: {closed_positions} positions, {closed_pending} pending orders", "green")
     