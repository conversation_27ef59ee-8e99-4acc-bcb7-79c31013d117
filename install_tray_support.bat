@echo off
REM Install System Tray Support for MyApp
REM This installs the required packages for built-in system tray functionality

echo Installing System Tray Support...
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please make sure 'venv' folder exists in the same directory as this batch file.
    echo.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install system tray dependencies
echo Installing pystray and Pillow...
pip install pystray>=0.19.4 Pillow>=9.0.0

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install system tray dependencies.
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)

echo.
echo ✅ System tray support installed successfully!
echo.
echo You can now use the "Minimize to Tray" button in your application.
echo The app will run in the background and you can restore it from the system tray.
echo.
pause
