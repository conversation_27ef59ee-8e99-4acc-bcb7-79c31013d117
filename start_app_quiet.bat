@echo off
REM MyApp Quiet Launcher - Starts minimized to tray with no windows
REM This batch file activates the virtual environment and runs the app quietly

REM Change to the script directory
cd /d "%~dp0"

REM Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo ERROR: Virtual environment not found!
    echo Please make sure 'venv' folder exists in the same directory as this batch file.
    pause
    exit /b 1
)

REM Check if main.py exists
if not exist "main-gui.py" (
    echo ERROR: main.py not found!
    echo Please make sure main.py is in the same directory as this batch file.
    pause
    exit /b 1
)

REM Activate virtual environment and run quietly
venv\Scripts\pythonw.exe main-gui.py --quiet --minimize

REM If we get here, there was likely an error
if errorlevel 1 (
    echo <PERSON> exited with an error.
    pause
)
