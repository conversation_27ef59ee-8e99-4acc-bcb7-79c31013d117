
import MetaTrader5 as mt5
import customtkinter as ctk

# ===========================
# Class: TabInstant
# ===========================
class TabInstant:
    def __init__(self, master, config, util):
        self.frame = master.add("Instant")
        self.config = config
        self.util = util
        self.symbol_var = ctk.StringVar(value="XU")
        self.lot_var = ctk.DoubleVar(value=0.02)
        self.point_sl_var = ctk.IntVar(value=self.config.SL_POINTS)
        self.point_tp_var = ctk.IntVar(value=self.config.TP2_POINTS)
        self.comment_var = ctk.StringVar(value="INSTANT")

        self.form1 = ctk.CTkFrame(self.frame)
        self.form1.pack(pady=20) 

        self.build_ui()

    def build_ui(self): 
        self.comment_var.set("INSTANT")  # Default Comment
        self.comment_label = ctk.CTkLabel(self.form1, text="Comment")
        self.comment_label.grid(row=0, column=0, padx=10, pady=5)
        self.comment_val = ctk.CTkEntry(self.form1, textvariable=self.comment_var)
        self.comment_val.grid(row=0, column=1, columnspan=3, sticky='ew', padx=10, pady=5)

        # Dropdown for selecting symbol
        self.symbol_label = ctk.CTkLabel(self.form1, text="Symbol:")
        self.symbol_label.grid(row=1, column=0, padx=10, pady=5)
        self.symbol_var.set("XU")  # Default symbol
        self.symbol_dropdown = ctk.CTkOptionMenu(self.form1, values=list(self.config.symbols), variable=self.symbol_var)
        self.symbol_dropdown.grid(row=1, column=1, padx=10, pady=5)

        self.lot_var.set(0.02)  # Default lot size
        self.lot_label = ctk.CTkLabel(self.form1, text="L Size:")
        self.lot_label.grid(row=1, column=2, padx=10, pady=5)
        self.lot_val = ctk.CTkEntry(self.form1, textvariable=self.lot_var)
        self.lot_val.grid(row=1, column=3, padx=10, pady=5)

        self.point_sl_var.set(self.config.SL_POINTS)  # Default sl size
        self.point_sl_label = ctk.CTkLabel(self.form1, text="SL Point:")
        self.point_sl_label.grid(row=2, column=0, padx=10, pady=5)
        self.point_sl_val = ctk.CTkEntry(self.form1, textvariable=self.point_sl_var)
        self.point_sl_val.grid(row=2, column=1, padx=10, pady=5)

        self.point_tp_var.set(self.config.SL_POINTS*2)  # Default tp size
        self.point_tp_label = ctk.CTkLabel(self.form1, text="TP Point:")
        self.point_tp_label.grid(row=2, column=2, padx=10, pady=5)
        self.point_tp_val = ctk.CTkEntry(self.form1, textvariable=self.point_tp_var)
        self.point_tp_val.grid(row=2, column=3, padx=10, pady=5)

        ctk.CTkButton(self.frame, text="<<", fg_color="red", command=lambda: self.submit_instant("Sell Now")).pack(side="left", padx=10)
        ctk.CTkButton(self.frame, text=">>", fg_color="green", command=lambda: self.submit_instant("Buy Now")).pack(side="right", padx=10)
 
    def submit_instant(self, order_type):
        try:
            symbol = self.util.get_symbol(self.symbol_var)
            point = mt5.symbol_info(symbol).point
            price = mt5.symbol_info_tick(symbol).ask if order_type == "Buy Now" else mt5.symbol_info_tick(symbol).bid
            sl = price - self.point_sl_var.get() * point if order_type == "Buy Now" else price + self.point_sl_var.get() * point
            tp = price + self.point_tp_var.get() * point if order_type == "Buy Now" else price - self.point_tp_var.get() * point
            lot = self.lot_var.get()
            self.util.send_order(order_type, symbol, lot, price, sl, tp, self.comment_var.get())
        except Exception as e:
            print(f"Instant order {symbol} error: {e}") 